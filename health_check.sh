#!/bin/bash

# 🔍 Project Chronos + Speechbot Health Check Script
# Comprehensive health monitoring for ADHD voice assistant platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
HEART="💙"
BRAIN="🧠"
ROCKET="🚀"

echo -e "${PURPLE}${ROCKET} Project Chronos + Speechbot Health Check${NC}"
echo -e "${CYAN}Comprehensive ADHD Voice Assistant Platform Monitoring${NC}"
echo "=================================================================="

# Function to print colored output
print_success() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_error() {
    echo -e "${RED}${CROSS} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

# Function to check HTTP endpoint
check_http() {
    local url=$1
    local name=$2
    local timeout=${3:-10}
    
    if curl -f -s --max-time $timeout "$url" > /dev/null 2>&1; then
        print_success "$name is accessible"
        return 0
    else
        print_error "$name is not accessible"
        return 1
    fi
}

# Function to check HTTP endpoint with JSON response
check_http_json() {
    local url=$1
    local name=$2
    local timeout=${3:-10}
    
    response=$(curl -f -s --max-time $timeout "$url" 2>/dev/null)
    if [ $? -eq 0 ] && echo "$response" | jq . > /dev/null 2>&1; then
        print_success "$name is responding with valid JSON"
        return 0
    else
        print_error "$name is not responding or returning invalid JSON"
        return 1
    fi
}

# Function to check Docker service
check_docker_service() {
    local service=$1
    
    if docker-compose ps | grep -q "$service.*Up"; then
        print_success "Docker service '$service' is running"
        return 0
    else
        print_error "Docker service '$service' is not running"
        return 1
    fi
}

# Function to check database connectivity
check_database() {
    print_info "Checking database connectivity..."
    
    if docker-compose exec -T postgres pg_isready -U chronos > /dev/null 2>&1; then
        print_success "PostgreSQL is ready"
        
        # Check database connection from API
        if docker-compose exec -T chronos-api python -c "
from app.core.database import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        conn.execute(text('SELECT 1'))
    print('Database connection successful')
    exit(0)
except Exception as e:
    print(f'Database connection failed: {e}')
    exit(1)
" > /dev/null 2>&1; then
            print_success "Database connection from API is working"
            return 0
        else
            print_error "Database connection from API failed"
            return 1
        fi
    else
        print_error "PostgreSQL is not ready"
        return 1
    fi
}

# Function to check Redis connectivity
check_redis() {
    print_info "Checking Redis connectivity..."
    
    if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
        print_success "Redis is responding"
        return 0
    else
        print_error "Redis is not responding"
        return 1
    fi
}

# Function to check Speechbot capabilities
check_speechbot_capabilities() {
    print_info "Checking Speechbot capabilities..."
    
    local url="http://speechbot.autism.localhost:8090/api/v1/tts/capabilities"
    response=$(curl -f -s --max-time 15 "$url" 2>/dev/null)
    
    if [ $? -eq 0 ] && echo "$response" | jq . > /dev/null 2>&1; then
        engine=$(echo "$response" | jq -r '.engine // "unknown"')
        model_size=$(echo "$response" | jq -r '.model_size // "unknown"')
        print_success "Speechbot capabilities: $engine ($model_size)"
        return 0
    else
        print_error "Speechbot capabilities check failed"
        return 1
    fi
}

# Function to test voice synthesis
test_voice_synthesis() {
    print_info "Testing voice synthesis..."
    
    local url="http://speechbot.autism.localhost:8090/api/v1/tts/quick"
    local test_data='{"text": "Health check test message", "mode": "calm"}'
    
    response=$(curl -f -s --max-time 30 -X POST \
        -H "Content-Type: application/json" \
        -d "$test_data" \
        "$url" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$response" ]; then
        audio_size=$(echo -n "$response" | wc -c)
        if [ "$audio_size" -gt 1000 ]; then
            print_success "Voice synthesis test successful (${audio_size} bytes)"
            return 0
        else
            print_warning "Voice synthesis returned small response (${audio_size} bytes)"
            return 1
        fi
    else
        print_error "Voice synthesis test failed"
        return 1
    fi
}

# Function to check GPU availability
check_gpu() {
    print_info "Checking GPU availability..."
    
    if command -v nvidia-smi > /dev/null 2>&1; then
        gpu_info=$(nvidia-smi --query-gpu=name,memory.used,memory.total --format=csv,noheader,nounits 2>/dev/null)
        if [ $? -eq 0 ]; then
            print_success "NVIDIA GPU detected: $gpu_info"
            
            # Check GPU usage in Speechbot container
            gpu_usage=$(docker-compose exec -T speechbot python -c "
import torch
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name(0)}')
    print(f'Memory: {torch.cuda.memory_allocated(0)/1024**3:.1f}GB / {torch.cuda.memory_reserved(0)/1024**3:.1f}GB')
else:
    print('CUDA not available')
" 2>/dev/null)
            
            if echo "$gpu_usage" | grep -q "GPU:"; then
                print_success "Speechbot GPU access: $gpu_usage"
            else
                print_warning "Speechbot may not be using GPU"
            fi
            return 0
        else
            print_warning "NVIDIA GPU detected but nvidia-smi failed"
            return 1
        fi
    else
        print_warning "No NVIDIA GPU detected (CPU-only mode)"
        return 0
    fi
}

# Function to check system resources
check_system_resources() {
    print_info "Checking system resources..."
    
    # Check memory usage
    if command -v free > /dev/null 2>&1; then
        memory_info=$(free -h | grep "Mem:")
        used_mem=$(echo $memory_info | awk '{print $3}')
        total_mem=$(echo $memory_info | awk '{print $2}')
        print_info "Memory usage: $used_mem / $total_mem"
    fi
    
    # Check disk usage
    if command -v df > /dev/null 2>&1; then
        disk_usage=$(df -h . | tail -1 | awk '{print $5 " used (" $3 "/" $2 ")"}')
        print_info "Disk usage: $disk_usage"
    fi
    
    # Check Docker resource usage
    if command -v docker > /dev/null 2>&1; then
        print_info "Docker container resource usage:"
        docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -10
    fi
}

# Function to check service logs for errors
check_service_logs() {
    print_info "Checking recent service logs for errors..."
    
    services=("chronos-api" "speechbot" "chronos-ui" "postgres" "redis")
    
    for service in "${services[@]}"; do
        error_count=$(docker-compose logs --tail=100 "$service" 2>/dev/null | grep -i "error\|exception\|failed" | wc -l)
        if [ "$error_count" -eq 0 ]; then
            print_success "$service: No recent errors"
        elif [ "$error_count" -lt 5 ]; then
            print_warning "$service: $error_count recent errors (check logs)"
        else
            print_error "$service: $error_count recent errors (needs attention)"
        fi
    done
}

# Function to check network connectivity
check_network() {
    print_info "Checking network connectivity..."
    
    # Check internal service communication
    if docker-compose exec -T chronos-api curl -f -s http://speechbot:8001/health > /dev/null 2>&1; then
        print_success "Internal service communication working"
    else
        print_error "Internal service communication failed"
    fi
    
    # Check external connectivity
    if curl -f -s --max-time 5 https://huggingface.co > /dev/null 2>&1; then
        print_success "External connectivity working"
    else
        print_warning "External connectivity may be limited"
    fi
}

# Main health check function
main_health_check() {
    local overall_status=0
    
    echo -e "${CYAN}🔍 Starting comprehensive health check...${NC}"
    echo ""
    
    # Docker services check
    print_info "Checking Docker services..."
    services=("chronos-api" "speechbot" "chronos-ui" "postgres" "redis" "traefik")
    for service in "${services[@]}"; do
        if ! check_docker_service "$service"; then
            overall_status=1
        fi
    done
    echo ""
    
    # Database connectivity
    if ! check_database; then
        overall_status=1
    fi
    echo ""
    
    # Redis connectivity
    if ! check_redis; then
        overall_status=1
    fi
    echo ""
    
    # HTTP endpoints check
    print_info "Checking HTTP endpoints..."
    endpoints=(
        "http://chronos.autism.localhost:8090|Main Platform"
        "http://api.autism.localhost:8090/health|API Health"
        "http://speechbot.autism.localhost:8090/health|Speechbot Health"
        "http://grafana.autism.localhost:8090|Grafana Dashboard"
        "http://traefik.autism.localhost:8091|Traefik Dashboard"
    )
    
    for endpoint in "${endpoints[@]}"; do
        url=$(echo "$endpoint" | cut -d'|' -f1)
        name=$(echo "$endpoint" | cut -d'|' -f2)
        if ! check_http "$url" "$name"; then
            overall_status=1
        fi
    done
    echo ""
    
    # Speechbot specific checks
    if ! check_speechbot_capabilities; then
        overall_status=1
    fi
    echo ""
    
    if ! test_voice_synthesis; then
        overall_status=1
    fi
    echo ""
    
    # GPU check
    check_gpu
    echo ""
    
    # System resources
    check_system_resources
    echo ""
    
    # Network connectivity
    check_network
    echo ""
    
    # Service logs
    check_service_logs
    echo ""
    
    # Overall status
    echo "=================================================================="
    if [ $overall_status -eq 0 ]; then
        echo -e "${GREEN}${CHECK} Overall Health Status: HEALTHY${NC}"
        echo -e "${CYAN}${HEART} Project Chronos + Speechbot is running optimally!${NC}"
        echo -e "${BLUE}${BRAIN} Ready to assist ADHD users with voice technology!${NC}"
    else
        echo -e "${RED}${CROSS} Overall Health Status: ISSUES DETECTED${NC}"
        echo -e "${YELLOW}${WARNING} Some components need attention. Check the errors above.${NC}"
    fi
    echo "=================================================================="
    
    return $overall_status
}

# Function to show quick status
quick_status() {
    echo -e "${CYAN}${ROCKET} Quick Status Check${NC}"
    echo "================================"
    
    # Check main services
    if check_http "http://chronos.autism.localhost:8090" "Main Platform" 5; then
        if check_http_json "http://speechbot.autism.localhost:8090/health" "Speechbot" 5; then
            echo -e "${GREEN}${CHECK} Platform Status: ONLINE${NC}"
        else
            echo -e "${YELLOW}${WARNING} Platform Status: PARTIAL${NC}"
        fi
    else
        echo -e "${RED}${CROSS} Platform Status: OFFLINE${NC}"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  --full, -f     Run comprehensive health check (default)"
    echo "  --quick, -q    Run quick status check"
    echo "  --help, -h     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Run full health check"
    echo "  $0 --quick      # Run quick status check"
}

# Parse command line arguments
case "${1:-}" in
    --quick|-q)
        quick_status
        ;;
    --help|-h)
        show_usage
        ;;
    --full|-f|"")
        main_health_check
        ;;
    *)
        echo "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac
