# Development shell for Project Chronos with AMD GPU support
# Run with: nix-shell

{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  name = "chronos-amd-dev";
  
  buildInputs = with pkgs; [
    # Core development tools
    git
    curl
    wget
    jq
    
    # Docker and containerization
    docker
    docker-compose
    
    # Python development
    python310
    python310Packages.pip
    python310Packages.virtualenv
    python310Packages.setuptools
    python310Packages.wheel
    
    # Node.js for frontend (if needed)
    nodejs_18
    yarn
    
    # Database tools
    postgresql
    redis
    
    # Monitoring tools
    htop
    iotop
    
    # AMD GPU tools
    rocminfo
    rocm-smi
    hip
    
    # Audio development
    ffmpeg
    sox
    alsa-utils
    
    # Build tools
    gcc
    cmake
    pkg-config
    
    # System libraries for Python packages
    zlib
    libffi
    openssl
    
    # Graphics libraries
    mesa
    vulkan-loader
    vulkan-tools
  ];
  
  # Environment variables for AMD GPU development
  shellHook = ''
    echo "🔥 Project Chronos AMD GPU Development Environment"
    echo "=================================================="
    echo ""
    
    # AMD GPU environment
    export ROC_ENABLE_PRE_VEGA=1
    export HSA_OVERRIDE_GFX_VERSION=11.0.0
    export PYTORCH_ROCM_ARCH=gfx1100,gfx1101,gfx1102
    export HIP_VISIBLE_DEVICES=0
    
    # Development environment
    export PYTHONPATH="$PWD:$PYTHONPATH"
    export PATH="$PWD/scripts:$PATH"
    
    # Docker environment
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    # Check AMD GPU status
    echo "🔍 Checking AMD GPU status..."
    if command -v rocm-smi &> /dev/null; then
        echo "✅ ROCm tools available"
        rocm-smi --showproductname 2>/dev/null || echo "⚠️ ROCm not fully configured"
    else
        echo "❌ ROCm tools not found"
    fi
    
    # Check Docker
    if command -v docker &> /dev/null; then
        echo "✅ Docker available"
        if docker info &> /dev/null; then
            echo "✅ Docker daemon running"
        else
            echo "⚠️ Docker daemon not running - start with: sudo systemctl start docker"
        fi
    else
        echo "❌ Docker not found"
    fi
    
    # Check GPU devices
    if [ -e "/dev/kfd" ] && [ -e "/dev/dri" ]; then
        echo "✅ GPU devices available: /dev/kfd, /dev/dri"
    else
        echo "⚠️ GPU devices not found - check NixOS configuration"
    fi
    
    echo ""
    echo "🚀 Ready for AMD GPU-accelerated development!"
    echo ""
    echo "Quick commands:"
    echo "  ./setup_amd_gpu.sh     - Run AMD GPU setup"
    echo "  docker-compose up -d   - Start all services"
    echo "  ./health_check.sh      - Check service health"
    echo ""
  '';
  
  # Additional environment variables
  NIX_ENFORCE_PURITY = 0;  # Allow impure operations for development
}
