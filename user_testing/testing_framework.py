#!/usr/bin/env python3
"""
ADHD User Testing Framework
Comprehensive testing suite for ADHD community feedback
"""

import asyncio
import logging
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TestUser:
    """Represents a test user with ADHD characteristics."""
    
    id: str
    name: str
    email: str
    adhd_type: str  # "inattentive", "hyperactive", "combined"
    severity: str   # "mild", "moderate", "severe"
    age_group: str  # "18-25", "26-35", "36-45", "46+"
    tech_comfort: str  # "low", "medium", "high"
    assistive_tech_experience: bool
    preferred_adhd_modes: List[str]
    accessibility_needs: List[str]
    consent_given: bool = False
    test_session_id: Optional[str] = None


@dataclass
class TestScenario:
    """Represents a specific testing scenario."""
    
    id: str
    name: str
    description: str
    category: str  # "voice_synthesis", "body_doubling", "emotion_detection", "ui_navigation"
    difficulty: str  # "easy", "medium", "hard"
    estimated_duration: int  # minutes
    instructions: List[str]
    success_criteria: List[str]
    adhd_specific_considerations: List[str]
    required_features: List[str]


@dataclass
class TestResult:
    """Represents results from a test scenario."""
    
    user_id: str
    scenario_id: str
    session_id: str
    start_time: datetime
    end_time: Optional[datetime]
    completed: bool
    success: bool
    difficulty_rating: int  # 1-10 scale
    satisfaction_rating: int  # 1-10 scale
    adhd_impact_rating: int  # 1-10 scale (how well it addressed ADHD needs)
    errors_encountered: List[str]
    positive_feedback: List[str]
    negative_feedback: List[str]
    suggestions: List[str]
    accessibility_issues: List[str]
    time_to_complete: Optional[float]  # seconds
    cognitive_load_rating: int  # 1-10 scale
    would_use_again: bool
    additional_notes: str


class ADHDTestingFramework:
    """
    Comprehensive testing framework for ADHD users.
    Manages test scenarios, user sessions, and feedback collection.
    """
    
    def __init__(self, data_dir: str = "user_testing/data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.test_users: Dict[str, TestUser] = {}
        self.test_scenarios: Dict[str, TestScenario] = {}
        self.test_results: List[TestResult] = []
        
        self._initialize_test_scenarios()
    
    def _initialize_test_scenarios(self):
        """Initialize predefined test scenarios."""
        
        scenarios = [
            TestScenario(
                id="voice_synthesis_basic",
                name="Basic Voice Synthesis",
                description="Test basic text-to-speech functionality with ADHD modes",
                category="voice_synthesis",
                difficulty="easy",
                estimated_duration=10,
                instructions=[
                    "Navigate to the Speechbot section",
                    "Enter a simple text message",
                    "Try different ADHD modes (calm, excited, focused)",
                    "Generate and listen to the audio",
                    "Rate the voice quality and emotional appropriateness"
                ],
                success_criteria=[
                    "Successfully navigate to Speechbot",
                    "Generate audio in at least 2 different ADHD modes",
                    "Audio plays without technical issues",
                    "User can distinguish between different modes"
                ],
                adhd_specific_considerations=[
                    "Clear navigation without overwhelming options",
                    "Immediate audio feedback",
                    "Visual indicators for different modes",
                    "No long waiting times"
                ],
                required_features=["text_synthesis", "adhd_modes", "audio_playback"]
            ),
            
            TestScenario(
                id="voice_profile_creation",
                name="Personal Voice Profile Creation",
                description="Test voice cloning functionality for self-compassion",
                category="voice_synthesis",
                difficulty="medium",
                estimated_duration=15,
                instructions=[
                    "Navigate to Voice Profiles section",
                    "Record a 10-second voice sample",
                    "Create a voice profile with a meaningful name",
                    "Test the created voice profile",
                    "Use your voice for a self-compassion message"
                ],
                success_criteria=[
                    "Successfully record voice sample",
                    "Voice profile created without errors",
                    "Generated audio resembles user's voice",
                    "User feels comfortable with the result"
                ],
                adhd_specific_considerations=[
                    "Clear recording instructions",
                    "Visual feedback during recording",
                    "Quality assessment and guidance",
                    "Emotional safety for self-voice use"
                ],
                required_features=["voice_recording", "voice_cloning", "profile_management"]
            ),
            
            TestScenario(
                id="body_doubling_session",
                name="Virtual Body Doubling Session",
                description="Test virtual companion for work sessions",
                category="body_doubling",
                difficulty="medium",
                estimated_duration=20,
                instructions=[
                    "Start a body doubling session",
                    "Set a work task and duration",
                    "Experience virtual companion encouragement",
                    "Pause and resume the session",
                    "Complete or stop the session"
                ],
                success_criteria=[
                    "Session starts without confusion",
                    "Companion provides appropriate encouragement",
                    "User feels supported during work",
                    "Session controls work as expected"
                ],
                adhd_specific_considerations=[
                    "Non-judgmental companion presence",
                    "Appropriate encouragement frequency",
                    "Easy session control",
                    "Genuine feeling of accountability"
                ],
                required_features=["body_doubling", "session_management", "companion_voice"]
            ),
            
            TestScenario(
                id="emotion_detection",
                name="Automatic Emotion Detection",
                description="Test real-time emotion detection and mode suggestions",
                category="emotion_detection",
                difficulty="medium",
                estimated_duration=12,
                instructions=[
                    "Enable auto-emotion detection",
                    "Type text expressing different emotions",
                    "Observe suggested ADHD mode changes",
                    "Generate speech with auto-detected modes",
                    "Evaluate accuracy of emotion detection"
                ],
                success_criteria=[
                    "Emotion detection activates correctly",
                    "Mode suggestions change based on text",
                    "Suggested modes feel appropriate",
                    "User trusts the automatic detection"
                ],
                adhd_specific_considerations=[
                    "Accurate emotional state recognition",
                    "Helpful rather than intrusive suggestions",
                    "Override capability when needed",
                    "Emotional validation through technology"
                ],
                required_features=["emotion_detection", "auto_mode_selection", "real_time_analysis"]
            ),
            
            TestScenario(
                id="ui_navigation_adhd",
                name="ADHD-Optimized UI Navigation",
                description="Test overall user interface for ADHD accessibility",
                category="ui_navigation",
                difficulty="easy",
                estimated_duration=15,
                instructions=[
                    "Explore all main sections of Speechbot",
                    "Use quick action buttons",
                    "Navigate between different features",
                    "Test accessibility features",
                    "Evaluate cognitive load and clarity"
                ],
                success_criteria=[
                    "Can navigate without getting lost",
                    "Features are discoverable",
                    "Interface feels intuitive",
                    "No overwhelming information density"
                ],
                adhd_specific_considerations=[
                    "Clear visual hierarchy",
                    "Minimal cognitive load",
                    "Consistent navigation patterns",
                    "Helpful guidance and tips"
                ],
                required_features=["navigation", "accessibility", "visual_design"]
            ),
            
            TestScenario(
                id="real_time_streaming",
                name="Real-Time Audio Streaming",
                description="Test advanced real-time streaming synthesis",
                category="voice_synthesis",
                difficulty="hard",
                estimated_duration=18,
                instructions=[
                    "Access real-time streaming feature",
                    "Start streaming synthesis",
                    "Experience immediate audio playback",
                    "Test pause/resume functionality",
                    "Evaluate streaming quality and responsiveness"
                ],
                success_criteria=[
                    "Streaming starts without delay",
                    "Audio plays smoothly in real-time",
                    "Controls respond immediately",
                    "No audio artifacts or interruptions"
                ],
                adhd_specific_considerations=[
                    "Immediate gratification",
                    "Reduced waiting anxiety",
                    "Smooth, uninterrupted experience",
                    "Clear progress indicators"
                ],
                required_features=["real_time_streaming", "audio_controls", "progress_tracking"]
            )
        ]
        
        for scenario in scenarios:
            self.test_scenarios[scenario.id] = scenario
    
    def register_test_user(
        self,
        name: str,
        email: str,
        adhd_type: str,
        severity: str,
        age_group: str,
        tech_comfort: str,
        assistive_tech_experience: bool,
        preferred_adhd_modes: List[str],
        accessibility_needs: List[str]
    ) -> TestUser:
        """Register a new test user."""
        
        user = TestUser(
            id=str(uuid.uuid4()),
            name=name,
            email=email,
            adhd_type=adhd_type,
            severity=severity,
            age_group=age_group,
            tech_comfort=tech_comfort,
            assistive_tech_experience=assistive_tech_experience,
            preferred_adhd_modes=preferred_adhd_modes,
            accessibility_needs=accessibility_needs
        )
        
        self.test_users[user.id] = user
        self._save_user_data(user)
        
        logger.info(f"Registered test user: {user.name} ({user.id})")
        return user
    
    def start_test_session(self, user_id: str) -> str:
        """Start a new test session for a user."""
        
        if user_id not in self.test_users:
            raise ValueError(f"User {user_id} not found")
        
        session_id = str(uuid.uuid4())
        self.test_users[user_id].test_session_id = session_id
        
        logger.info(f"Started test session {session_id} for user {user_id}")
        return session_id
    
    def get_recommended_scenarios(self, user_id: str) -> List[TestScenario]:
        """Get recommended test scenarios for a user based on their profile."""
        
        if user_id not in self.test_users:
            raise ValueError(f"User {user_id} not found")
        
        user = self.test_users[user_id]
        recommended = []
        
        # Always include basic scenarios
        recommended.append(self.test_scenarios["voice_synthesis_basic"])
        recommended.append(self.test_scenarios["ui_navigation_adhd"])
        
        # Add scenarios based on user characteristics
        if user.tech_comfort in ["medium", "high"]:
            recommended.append(self.test_scenarios["voice_profile_creation"])
            recommended.append(self.test_scenarios["emotion_detection"])
        
        if user.assistive_tech_experience:
            recommended.append(self.test_scenarios["body_doubling_session"])
        
        if user.tech_comfort == "high":
            recommended.append(self.test_scenarios["real_time_streaming"])
        
        return recommended
    
    def record_test_result(self, result: TestResult):
        """Record results from a test scenario."""
        
        self.test_results.append(result)
        self._save_test_result(result)
        
        logger.info(f"Recorded test result for scenario {result.scenario_id}")
    
    def generate_user_report(self, user_id: str) -> Dict[str, Any]:
        """Generate a comprehensive report for a user's testing session."""
        
        if user_id not in self.test_users:
            raise ValueError(f"User {user_id} not found")
        
        user = self.test_users[user_id]
        user_results = [r for r in self.test_results if r.user_id == user_id]
        
        if not user_results:
            return {"error": "No test results found for user"}
        
        # Calculate metrics
        total_scenarios = len(user_results)
        completed_scenarios = len([r for r in user_results if r.completed])
        successful_scenarios = len([r for r in user_results if r.success])
        
        avg_difficulty = sum(r.difficulty_rating for r in user_results) / total_scenarios
        avg_satisfaction = sum(r.satisfaction_rating for r in user_results) / total_scenarios
        avg_adhd_impact = sum(r.adhd_impact_rating for r in user_results) / total_scenarios
        avg_cognitive_load = sum(r.cognitive_load_rating for r in user_results) / total_scenarios
        
        # Collect feedback
        all_positive = []
        all_negative = []
        all_suggestions = []
        all_accessibility_issues = []
        
        for result in user_results:
            all_positive.extend(result.positive_feedback)
            all_negative.extend(result.negative_feedback)
            all_suggestions.extend(result.suggestions)
            all_accessibility_issues.extend(result.accessibility_issues)
        
        report = {
            "user_profile": asdict(user),
            "session_summary": {
                "total_scenarios": total_scenarios,
                "completed_scenarios": completed_scenarios,
                "successful_scenarios": successful_scenarios,
                "completion_rate": completed_scenarios / total_scenarios * 100,
                "success_rate": successful_scenarios / completed_scenarios * 100 if completed_scenarios > 0 else 0
            },
            "average_ratings": {
                "difficulty": round(avg_difficulty, 2),
                "satisfaction": round(avg_satisfaction, 2),
                "adhd_impact": round(avg_adhd_impact, 2),
                "cognitive_load": round(avg_cognitive_load, 2)
            },
            "feedback_summary": {
                "positive_feedback": list(set(all_positive)),
                "negative_feedback": list(set(all_negative)),
                "suggestions": list(set(all_suggestions)),
                "accessibility_issues": list(set(all_accessibility_issues))
            },
            "scenario_results": [asdict(r) for r in user_results],
            "recommendations": self._generate_user_recommendations(user, user_results)
        }
        
        return report
    
    def generate_aggregate_report(self) -> Dict[str, Any]:
        """Generate aggregate report across all users."""
        
        if not self.test_results:
            return {"error": "No test results available"}
        
        # User demographics
        user_demographics = {
            "total_users": len(self.test_users),
            "adhd_types": {},
            "severity_levels": {},
            "age_groups": {},
            "tech_comfort_levels": {}
        }
        
        for user in self.test_users.values():
            user_demographics["adhd_types"][user.adhd_type] = user_demographics["adhd_types"].get(user.adhd_type, 0) + 1
            user_demographics["severity_levels"][user.severity] = user_demographics["severity_levels"].get(user.severity, 0) + 1
            user_demographics["age_groups"][user.age_group] = user_demographics["age_groups"].get(user.age_group, 0) + 1
            user_demographics["tech_comfort_levels"][user.tech_comfort] = user_demographics["tech_comfort_levels"].get(user.tech_comfort, 0) + 1
        
        # Overall metrics
        total_tests = len(self.test_results)
        completed_tests = len([r for r in self.test_results if r.completed])
        successful_tests = len([r for r in self.test_results if r.success])
        
        avg_satisfaction = sum(r.satisfaction_rating for r in self.test_results) / total_tests
        avg_adhd_impact = sum(r.adhd_impact_rating for r in self.test_results) / total_tests
        avg_cognitive_load = sum(r.cognitive_load_rating for r in self.test_results) / total_tests
        
        # Scenario performance
        scenario_performance = {}
        for scenario_id in self.test_scenarios.keys():
            scenario_results = [r for r in self.test_results if r.scenario_id == scenario_id]
            if scenario_results:
                scenario_performance[scenario_id] = {
                    "total_attempts": len(scenario_results),
                    "completion_rate": len([r for r in scenario_results if r.completed]) / len(scenario_results) * 100,
                    "success_rate": len([r for r in scenario_results if r.success]) / len(scenario_results) * 100,
                    "avg_satisfaction": sum(r.satisfaction_rating for r in scenario_results) / len(scenario_results),
                    "avg_difficulty": sum(r.difficulty_rating for r in scenario_results) / len(scenario_results)
                }
        
        # Common feedback themes
        all_positive = []
        all_negative = []
        all_suggestions = []
        all_accessibility_issues = []
        
        for result in self.test_results:
            all_positive.extend(result.positive_feedback)
            all_negative.extend(result.negative_feedback)
            all_suggestions.extend(result.suggestions)
            all_accessibility_issues.extend(result.accessibility_issues)
        
        report = {
            "user_demographics": user_demographics,
            "overall_metrics": {
                "total_tests": total_tests,
                "completion_rate": completed_tests / total_tests * 100,
                "success_rate": successful_tests / completed_tests * 100 if completed_tests > 0 else 0,
                "avg_satisfaction": round(avg_satisfaction, 2),
                "avg_adhd_impact": round(avg_adhd_impact, 2),
                "avg_cognitive_load": round(avg_cognitive_load, 2)
            },
            "scenario_performance": scenario_performance,
            "common_feedback": {
                "most_positive": self._get_most_common(all_positive, 10),
                "most_negative": self._get_most_common(all_negative, 10),
                "top_suggestions": self._get_most_common(all_suggestions, 10),
                "accessibility_issues": self._get_most_common(all_accessibility_issues, 10)
            },
            "recommendations": self._generate_aggregate_recommendations()
        }
        
        return report
    
    def _save_user_data(self, user: TestUser):
        """Save user data to file."""
        user_file = self.data_dir / f"user_{user.id}.json"
        with open(user_file, 'w') as f:
            json.dump(asdict(user), f, indent=2)
    
    def _save_test_result(self, result: TestResult):
        """Save test result to file."""
        result_file = self.data_dir / f"result_{result.user_id}_{result.scenario_id}_{int(time.time())}.json"
        
        # Convert datetime objects to strings
        result_dict = asdict(result)
        result_dict['start_time'] = result.start_time.isoformat()
        if result.end_time:
            result_dict['end_time'] = result.end_time.isoformat()
        
        with open(result_file, 'w') as f:
            json.dump(result_dict, f, indent=2)
    
    def _generate_user_recommendations(self, user: TestUser, results: List[TestResult]) -> List[str]:
        """Generate personalized recommendations for a user."""
        recommendations = []
        
        # Analyze results for patterns
        avg_cognitive_load = sum(r.cognitive_load_rating for r in results) / len(results)
        avg_satisfaction = sum(r.satisfaction_rating for r in results) / len(results)
        
        if avg_cognitive_load > 7:
            recommendations.append("Consider simplifying the interface further for this user's cognitive load preferences")
        
        if avg_satisfaction < 6:
            recommendations.append("Focus on addressing specific pain points identified in feedback")
        
        # ADHD-specific recommendations
        if user.adhd_type == "inattentive":
            recommendations.append("Emphasize features that support sustained attention and reduce distractions")
        elif user.adhd_type == "hyperactive":
            recommendations.append("Provide more interactive and engaging elements to channel energy")
        
        return recommendations
    
    def _generate_aggregate_recommendations(self) -> List[str]:
        """Generate recommendations based on aggregate data."""
        recommendations = [
            "Continue developing ADHD-specific features based on positive user feedback",
            "Address common accessibility issues identified across users",
            "Focus on scenarios with lower success rates for improvement",
            "Expand user testing to include more diverse ADHD presentations"
        ]
        
        return recommendations
    
    def _get_most_common(self, items: List[str], limit: int) -> List[tuple]:
        """Get most common items from a list."""
        from collections import Counter
        counter = Counter(items)
        return counter.most_common(limit)


# Example usage and test data generation
def create_sample_test_users(framework: ADHDTestingFramework) -> List[TestUser]:
    """Create sample test users for demonstration."""
    
    sample_users = [
        {
            "name": "Alex Chen",
            "email": "<EMAIL>",
            "adhd_type": "combined",
            "severity": "moderate",
            "age_group": "26-35",
            "tech_comfort": "high",
            "assistive_tech_experience": True,
            "preferred_adhd_modes": ["focused", "calm"],
            "accessibility_needs": ["high_contrast", "clear_navigation"]
        },
        {
            "name": "Jordan Smith",
            "email": "<EMAIL>",
            "adhd_type": "inattentive",
            "severity": "mild",
            "age_group": "18-25",
            "tech_comfort": "medium",
            "assistive_tech_experience": False,
            "preferred_adhd_modes": ["calm", "motivated"],
            "accessibility_needs": ["simple_interface", "clear_instructions"]
        },
        {
            "name": "Sam Rodriguez",
            "email": "<EMAIL>",
            "adhd_type": "hyperactive",
            "severity": "severe",
            "age_group": "36-45",
            "tech_comfort": "low",
            "assistive_tech_experience": True,
            "preferred_adhd_modes": ["excited", "motivated"],
            "accessibility_needs": ["large_buttons", "voice_guidance"]
        }
    ]
    
    users = []
    for user_data in sample_users:
        user = framework.register_test_user(**user_data)
        users.append(user)
    
    return users


if __name__ == "__main__":
    # Initialize testing framework
    framework = ADHDTestingFramework()
    
    # Create sample users
    sample_users = create_sample_test_users(framework)
    
    print("🧪 ADHD User Testing Framework Initialized")
    print(f"📊 {len(framework.test_scenarios)} test scenarios available")
    print(f"👥 {len(sample_users)} sample users registered")
    
    # Show available scenarios
    print("\n📋 Available Test Scenarios:")
    for scenario in framework.test_scenarios.values():
        print(f"  • {scenario.name} ({scenario.difficulty}) - {scenario.estimated_duration}min")
    
    print("\n🚀 Ready for ADHD community testing!")
    print("Use the framework to:")
    print("  1. Register real test users")
    print("  2. Conduct guided testing sessions")
    print("  3. Collect comprehensive feedback")
    print("  4. Generate insights and recommendations")
