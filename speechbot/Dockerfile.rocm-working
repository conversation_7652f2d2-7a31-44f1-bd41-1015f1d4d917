# AMD ROCm Optimized Speechbot for RX 7900 XTX
# Working version with proper PyTorch ROCm

FROM python:3.10-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3-dev \
    python3-pip \
    git \
    wget \
    curl \
    build-essential \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Install PyTorch ROCm FIRST and LOCK the version
RUN pip3 install --no-cache-dir \
    torch==2.3.1+rocm5.7 \
    torchaudio==2.3.1+rocm5.7 \
    --index-url https://download.pytorch.org/whl/rocm5.7

# Install core dependencies without torch conflicts
RUN pip3 install --no-cache-dir \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.0 \
    pydantic==2.5.0 \
    structlog==23.2.0 \
    httpx==0.25.2 \
    aiofiles==23.2.1 \
    python-multipart==0.0.6

# Install audio processing libraries
RUN pip3 install --no-cache-dir \
    librosa==0.10.1 \
    soundfile==0.12.1 \
    pydub==0.25.1 \
    scipy==1.11.3 \
    numpy==1.24.3

# Install ML libraries (compatible with our PyTorch)
RUN pip3 install --no-cache-dir \
    transformers==4.35.0 \
    accelerate==0.24.0 \
    datasets==2.14.0

# Copy application code
COPY speechbot/ ./speechbot/
COPY app/core/ ./app/core/
COPY app/models/ ./app/models/

# Create directories
RUN mkdir -p /app/models /app/cache /app/audio_samples

# Set environment variables for AMD GPU
ENV PYTORCH_ROCM_ARCH=gfx1100,gfx1101,gfx1102
ENV HSA_OVERRIDE_GFX_VERSION=11.0.0
ENV HIP_VISIBLE_DEVICES=0
ENV CUDA_VISIBLE_DEVICES=0

# Set up paths
ENV PYTHONPATH=/app
ENV AUDIO_SAMPLES_PATH=/app/audio_samples

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run the application
CMD ["python3", "-m", "speechbot.main"]
