"""
Speechbot Main Application
ADHD-Optimized Voice Assistant with Dia TTS Integration
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse, HTMLResponse
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi

from speechbot.core.config import settings
from speechbot.core.logging import setup_logging
from speechbot.api.v1 import tts, voice_profiles, dialogue, preferences, streaming
from speechbot.services.dia_engine import DiaEngine
from speechbot.services.database import db_service
from speechbot.core.metrics import setup_metrics

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    logger.info("Starting Speechbot with Dia TTS...")

    # Initialize database
    try:
        await db_service.initialize()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

    # Initialize Dia engine
    try:
        dia_engine = DiaEngine()
        await dia_engine.initialize()
        app.state.dia_engine = dia_engine
        logger.info("Dia TTS engine initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Dia engine: {e}")
        raise

    # Setup metrics
    setup_metrics()
    
    yield
    
    # Cleanup
    logger.info("Shutting down Speechbot...")
    if hasattr(app.state, 'dia_engine'):
        await app.state.dia_engine.cleanup()
    await db_service.cleanup()


# Create FastAPI application
app = FastAPI(
    title="Speechbot - ADHD Voice Assistant",
    description="🎭 ADHD-optimized voice synthesis using Dia TTS with 5 emotional modes",
    version="1.0.0",
    docs_url=None,  # Custom dark mode docs
    redoc_url=None,  # Custom dark mode redoc
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Enhanced CORS middleware for API documentation and frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Next.js dev server
        "http://localhost:8090",  # Traefik proxy
        "http://chronos.autism.localhost:8090",  # Main UI
        "http://api.autism.localhost:8090",  # API docs
        "http://speechbot.autism.localhost:8090",  # Speechbot UI
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8090",
        "http://127.0.0.1:8001",  # Direct Speechbot access
        *settings.ALLOWED_HOSTS,  # Additional configured hosts
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "X-API-Key",
        "Cache-Control",
        "Pragma",
        "X-ADHD-Mode",
        "X-Voice-Profile",
        "X-Stream-ID",
    ],
    expose_headers=[
        "X-Audio-Duration",
        "X-Sample-Rate",
        "X-ADHD-Mode",
        "X-Voice-Profile",
        "X-Generation-Time",
        "X-Quality-Score",
        "X-Streaming",
        "X-Chunk-Index",
    ],
)

# Include routers
app.include_router(
    tts.router,
    prefix="/api/v1/tts",
    tags=["text-to-speech"]
)

app.include_router(
    voice_profiles.router,
    prefix="/api/v1/voice-profiles",
    tags=["voice-profiles"]
)

app.include_router(
    dialogue.router,
    prefix="/api/v1/dialogue",
    tags=["dialogue-generation"]
)

app.include_router(
    preferences.router,
    prefix="/api/v1/preferences",
    tags=["user-preferences"]
)

app.include_router(
    streaming.router,
    prefix="/api/v1/streaming",
    tags=["real-time-streaming"]
)


# Custom dark mode Swagger UI
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """Custom dark mode Swagger UI for ADHD-friendly Speechbot documentation."""
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - Interactive API Documentation",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
        swagger_ui_parameters={
            "theme": "dark",
            "syntaxHighlight.theme": "tomorrow-night",
            "tryItOutEnabled": True,
            "displayRequestDuration": True,
            "filter": True,
            "showExtensions": True,
            "showCommonExtensions": True,
            "deepLinking": True,
            "displayOperationId": False,
            "defaultModelsExpandDepth": 3,
            "defaultModelExpandDepth": 3,
            "docExpansion": "list",
            "persistAuthorization": True,
            "layout": "BaseLayout",
        }
    )

# Custom dark mode ReDoc
@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    """Custom dark mode ReDoc for comprehensive Speechbot API documentation."""
    return get_redoc_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - API Reference",
        redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@2.1.3/bundles/redoc.standalone.js",
        redoc_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
        with_google_fonts=True,
    )

# Enhanced OpenAPI schema
def custom_openapi():
    """Generate custom OpenAPI schema with ADHD-specific voice synthesis documentation."""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description="""
# 🎭 Speechbot - ADHD Voice Assistant API

**Advanced Voice Synthesis with ADHD-Optimized Emotional Modes**

## 🌟 Features

- **🎤 Voice Cloning** from 5-10 second audio samples
- **🧠 5 ADHD Emotional Modes** (Calm, Excited, Focused, Overwhelmed, Motivated)
- **⚡ Real-Time Streaming** for immediate audio feedback
- **🤝 Multi-Speaker Dialogue** generation for body doubling
- **🎵 Nonverbal Communication** (sighs, pauses, natural sounds)
- **📊 Quality Metrics** and performance monitoring

## 🎯 ADHD Emotional Modes

### 😌 Calm Mode
- **Use Case**: Overwhelm, anxiety, bedtime routines
- **Voice**: Slow, steady pace; lower pitch; gentle tone
- **Best For**: Meditation, studying, anxiety management

### ⚡ Excited Mode
- **Use Case**: Need motivation, starting projects, celebrating
- **Voice**: Faster pace; higher energy; enthusiastic tone
- **Best For**: Exercise, creative work, goal setting

### 🎯 Focused Mode
- **Use Case**: Deep work, important tasks, decision making
- **Voice**: Clear articulation; steady rhythm; confident tone
- **Best For**: Work tasks, learning, problem-solving

### 🛡️ Overwhelmed Mode
- **Use Case**: Feeling stressed, need comfort, recovery
- **Voice**: Very gentle; slower pace; nurturing tone
- **Best For**: Self-compassion, recovery, emotional support

### 🧠 Motivated Mode
- **Use Case**: Goal achievement, building confidence
- **Voice**: Strong; encouraging; empowering tone
- **Best For**: Affirmations, goal reminders, confidence building

## 🚀 Quick Start

1. **Test Basic Synthesis**: `POST /api/v1/tts/quick`
2. **Create Voice Profile**: `POST /api/v1/voice-profiles/create`
3. **Try Real-Time Streaming**: `POST /api/v1/streaming/synthesize`
4. **Start Body Doubling**: `POST /api/v1/dialogue/body-doubling`

## 🔊 Audio Formats

- **Primary**: WAV (24kHz, 16-bit)
- **Streaming**: Real-time chunks with metadata
- **Quality**: Professional-grade synthesis with Dia TTS

## 📞 Support

- **Documentation**: Complete guides in main platform docs
- **Community**: ADHD-focused user community
- **Research**: Evidence-based feature development
        """,
        routes=app.routes,
    )

    # Add ADHD-specific extensions
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }

    # Add server information
    openapi_schema["servers"] = [
        {
            "url": "http://speechbot.autism.localhost:8090",
            "description": "Speechbot service (Traefik proxy)"
        },
        {
            "url": "http://localhost:8001",
            "description": "Direct Speechbot service"
        }
    ]

    # Add ADHD-specific response headers
    openapi_schema["components"]["headers"] = {
        "X-ADHD-Mode": {
            "description": "The ADHD emotional mode used for synthesis",
            "schema": {"type": "string", "enum": ["calm", "excited", "focused", "overwhelmed", "motivated"]}
        },
        "X-Audio-Duration": {
            "description": "Duration of generated audio in seconds",
            "schema": {"type": "number"}
        },
        "X-Generation-Time": {
            "description": "Time taken to generate audio in seconds",
            "schema": {"type": "number"}
        },
        "X-Quality-Score": {
            "description": "Quality score of generated audio (0.0-1.0)",
            "schema": {"type": "number", "minimum": 0.0, "maximum": 1.0}
        }
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check if Dia engine is available
        if not hasattr(app.state, 'dia_engine'):
            raise HTTPException(status_code=503, detail="Dia engine not initialized")
        
        # Basic engine health check
        engine_status = await app.state.dia_engine.health_check()
        
        return {
            "status": "healthy",
            "service": "speechbot",
            "version": "1.0.0",
            "dia_engine": engine_status,
            "timestamp": asyncio.get_event_loop().time()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint."""
    try:
        if not hasattr(app.state, 'dia_engine'):
            return JSONResponse(
                status_code=503,
                content={"status": "not ready", "reason": "Dia engine not initialized"}
            )
        
        # Check if engine is ready for requests
        is_ready = await app.state.dia_engine.is_ready()
        
        if is_ready:
            return {"status": "ready", "service": "speechbot"}
        else:
            return JSONResponse(
                status_code=503,
                content={"status": "not ready", "reason": "Dia engine not ready"}
            )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={"status": "not ready", "reason": str(e)}
        )


@app.get("/")
async def root():
    """Root endpoint with comprehensive service information."""
    return {
        "service": "Speechbot",
        "description": "🎭 ADHD-Optimized Voice Assistant with Dia TTS",
        "version": "1.0.0",
        "tagline": "The world's most advanced ADHD voice synthesis platform",
        "features": [
            "🎤 Voice cloning from 5-10 second samples",
            "🧠 5 ADHD emotional modes (Calm, Excited, Focused, Overwhelmed, Motivated)",
            "⚡ Real-time streaming synthesis",
            "🤝 Multi-speaker dialogue generation for body doubling",
            "🎵 Nonverbal communication (laughs, sighs, natural pauses)",
            "📊 Quality metrics and performance monitoring",
            "🔊 Professional-grade audio with Dia TTS (1.6B parameters)"
        ],
        "adhd_modes": {
            "calm": "😌 For overwhelm, anxiety, relaxation",
            "excited": "⚡ For motivation, energy, celebration",
            "focused": "🎯 For work, concentration, clarity",
            "overwhelmed": "🛡️ For stress, comfort, support",
            "motivated": "🧠 For goals, confidence, achievement"
        },
        "documentation": {
            "interactive": "/docs",
            "reference": "/redoc",
            "openapi": "/openapi.json"
        },
        "endpoints": {
            "health": "/health",
            "ready": "/ready",
            "quick_synthesis": "/api/v1/tts/quick",
            "advanced_synthesis": "/api/v1/tts/synthesize",
            "streaming": "/api/v1/streaming/synthesize",
            "voice_profiles": "/api/v1/voice-profiles",
            "body_doubling": "/api/v1/dialogue/body-doubling",
            "preferences": "/api/v1/preferences"
        },
        "technical_specs": {
            "engine": "Dia TTS (1.6B parameters)",
            "sample_rate": "24kHz",
            "bit_depth": "16-bit",
            "formats": ["WAV", "streaming chunks"],
            "latency": "< 1 second for streaming",
            "quality": "Professional-grade synthesis"
        },
        "accessibility": "WCAG 2.1 AA compliant",
        "research_backed": True
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": "An unexpected error occurred",
            "type": type(exc).__name__
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "speechbot.main:app",
        host="0.0.0.0",
        port=8001,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
        access_log=True
    )
