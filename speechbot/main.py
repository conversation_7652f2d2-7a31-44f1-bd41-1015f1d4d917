"""
Speechbot Main Application
ADHD-Optimized Voice Assistant with Dia TTS Integration
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager

import uvicorn
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from speechbot.core.config import settings
from speechbot.core.logging import setup_logging
from speechbot.api.v1 import tts, voice_profiles, dialogue
from speechbot.services.dia_engine import DiaEngine
from speechbot.core.metrics import setup_metrics

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    logger.info("Starting Speechbot with Dia TTS...")
    
    # Initialize Dia engine
    try:
        dia_engine = DiaEngine()
        await dia_engine.initialize()
        app.state.dia_engine = dia_engine
        logger.info("Dia TTS engine initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Dia engine: {e}")
        raise
    
    # Setup metrics
    setup_metrics()
    
    yield
    
    # Cleanup
    logger.info("Shutting down Speechbot...")
    if hasattr(app.state, 'dia_engine'):
        await app.state.dia_engine.cleanup()


# Create FastAPI application
app = FastAPI(
    title="Speechbot - ADHD Voice Assistant",
    description="ADHD-optimized voice synthesis using Dia TTS",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(
    tts.router,
    prefix="/api/v1/tts",
    tags=["text-to-speech"]
)

app.include_router(
    voice_profiles.router,
    prefix="/api/v1/voice-profiles",
    tags=["voice-profiles"]
)

app.include_router(
    dialogue.router,
    prefix="/api/v1/dialogue",
    tags=["dialogue-generation"]
)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check if Dia engine is available
        if not hasattr(app.state, 'dia_engine'):
            raise HTTPException(status_code=503, detail="Dia engine not initialized")
        
        # Basic engine health check
        engine_status = await app.state.dia_engine.health_check()
        
        return {
            "status": "healthy",
            "service": "speechbot",
            "version": "1.0.0",
            "dia_engine": engine_status,
            "timestamp": asyncio.get_event_loop().time()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint."""
    try:
        if not hasattr(app.state, 'dia_engine'):
            return JSONResponse(
                status_code=503,
                content={"status": "not ready", "reason": "Dia engine not initialized"}
            )
        
        # Check if engine is ready for requests
        is_ready = await app.state.dia_engine.is_ready()
        
        if is_ready:
            return {"status": "ready", "service": "speechbot"}
        else:
            return JSONResponse(
                status_code=503,
                content={"status": "not ready", "reason": "Dia engine not ready"}
            )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={"status": "not ready", "reason": str(e)}
        )


@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": "Speechbot",
        "description": "ADHD-Optimized Voice Assistant with Dia TTS",
        "version": "1.0.0",
        "features": [
            "Voice cloning from 5-10 second samples",
            "Multi-speaker dialogue generation",
            "Nonverbal communication (laughs, sighs, etc.)",
            "ADHD-optimized emotional tone conditioning",
            "Real-time speech synthesis"
        ],
        "endpoints": {
            "docs": "/docs",
            "health": "/health",
            "ready": "/ready",
            "tts": "/api/v1/tts",
            "voice_profiles": "/api/v1/voice-profiles",
            "dialogue": "/api/v1/dialogue"
        }
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "detail": "An unexpected error occurred",
            "type": type(exc).__name__
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "speechbot.main:app",
        host="0.0.0.0",
        port=8001,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
        access_log=True
    )
