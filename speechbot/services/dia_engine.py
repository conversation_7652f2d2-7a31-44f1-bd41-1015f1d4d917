"""
Dia TTS Engine Service
Core integration with Nari Labs Dia 1.6B parameter model for ADHD-optimized voice synthesis
"""

import asyncio
import logging
import os
import time
from typing import Dict, List, Optional, Tuple, Union
import torch
import torchaudio
import numpy as np
from pathlib import Path

# Real Dia TTS imports
try:
    from dia.model import Dia
    DIA_AVAILABLE = True
except ImportError:
    DIA_AVAILABLE = False
    logger.warning("Dia TTS not available. Install with: pip install git+https://github.com/nari-labs/dia.git")

from speechbot.core.config import settings, DiaConfig, ADHDConfig
from speechbot.core.metrics import tts_metrics

logger = logging.getLogger(__name__)


class DiaEngine:
    """
    Dia TTS Engine for ADHD-optimized voice synthesis.
    
    Provides voice cloning, dialogue generation, emotion control,
    and nonverbal communication capabilities.
    """
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = settings.DIA_DEVICE
        self.is_initialized = False
        self.voice_profiles = {}
        self.generation_lock = asyncio.Lock()
        
        # Performance tracking
        self.generation_count = 0
        self.total_generation_time = 0.0
        
    async def initialize(self):
        """Initialize the Dia TTS engine."""
        try:
            logger.info("Initializing Dia TTS engine...")
            
            # Check GPU availability
            if self.device == "cuda" and not torch.cuda.is_available():
                logger.warning("CUDA not available, falling back to CPU")
                self.device = "cpu"
            
            # Load model and tokenizer
            await self._load_model()
            
            # Initialize voice profiles cache
            self.voice_profiles = {}
            
            # Warm up the model
            await self._warmup_model()
            
            self.is_initialized = True
            logger.info("Dia TTS engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Dia engine: {e}")
            raise
    
    async def _load_model(self):
        """Load the Dia model and tokenizer."""
        try:
            if not DIA_AVAILABLE:
                raise ImportError("Dia TTS not available. Please install with: pip install git+https://github.com/nari-labs/dia.git")

            logger.info(f"Loading Dia model: {DiaConfig.MODEL_NAME}")

            # Load the real Dia model
            self.model = Dia.from_pretrained(
                DiaConfig.MODEL_NAME,
                compute_dtype=DiaConfig.COMPUTE_DTYPE,
                device=self.device
            )

            # Enable torch.compile if requested and supported
            if DiaConfig.USE_TORCH_COMPILE and hasattr(torch, 'compile') and self.device != "mps":
                logger.info("Compiling model with torch.compile for faster inference")
                # Note: torch.compile may not work with all Dia components, so we'll be careful
                try:
                    self.model = torch.compile(self.model)
                    logger.info("Model compilation successful")
                except Exception as e:
                    logger.warning(f"Model compilation failed, continuing without: {e}")

            # Tokenizer is handled internally by Dia model
            self.tokenizer = None  # Dia handles tokenization internally

            logger.info("Dia model loaded successfully")

        except Exception as e:
            logger.error(f"Failed to load Dia model: {e}")
            # Fallback to mock implementation for development
            logger.warning("Falling back to mock implementation for development")
            self.model = MockDiaModel(self.device)
            self.tokenizer = MockDiaTokenizer()
            raise
    
    async def _warmup_model(self):
        """Warm up the model with a test generation."""
        try:
            logger.info("Warming up Dia model...")
            
            test_text = "Hello, this is a test of the Dia TTS system."
            await self.synthesize_speech(
                text=test_text,
                voice_profile="default",
                adhd_mode="calm"
            )
            
            logger.info("Model warmup completed")
            
        except Exception as e:
            logger.warning(f"Model warmup failed: {e}")
    
    async def synthesize_speech(
        self,
        text: str,
        voice_profile: str = "default",
        adhd_mode: str = "calm",
        include_nonverbals: bool = True,
        speaker_id: str = "[S1]"
    ) -> Tuple[np.ndarray, int]:
        """
        Synthesize speech from text with ADHD optimizations.
        
        Args:
            text: Text to synthesize
            voice_profile: Voice profile to use
            adhd_mode: ADHD emotional state (calm, excited, focused, etc.)
            include_nonverbals: Whether to include nonverbal sounds
            speaker_id: Speaker identifier for dialogue
            
        Returns:
            Tuple of (audio_array, sample_rate)
        """
        if not self.is_initialized:
            raise RuntimeError("Dia engine not initialized")
        
        start_time = time.time()
        
        try:
            async with self.generation_lock:
                # Prepare text with ADHD optimizations
                processed_text = await self._prepare_text_for_adhd(
                    text, adhd_mode, include_nonverbals, speaker_id
                )
                
                # Get generation parameters for ADHD mode
                gen_params = self._get_adhd_generation_params(adhd_mode)
                
                # Generate audio
                audio_array, sample_rate = await self._generate_audio(
                    processed_text, voice_profile, gen_params
                )
                
                # Post-process for ADHD optimization
                audio_array = await self._post_process_audio(
                    audio_array, adhd_mode
                )
                
                # Update metrics
                generation_time = time.time() - start_time
                self.generation_count += 1
                self.total_generation_time += generation_time
                
                # Record metrics
                tts_metrics.synthesis_requests_total.labels(
                    voice_profile=voice_profile,
                    adhd_mode=adhd_mode
                ).inc()
                
                tts_metrics.synthesis_duration_seconds.labels(
                    text_length_bucket=self._get_text_length_bucket(len(text))
                ).observe(generation_time)
                
                logger.info(
                    f"Generated speech: {len(text)} chars, "
                    f"{generation_time:.2f}s, mode: {adhd_mode}"
                )
                
                return audio_array, sample_rate
                
        except Exception as e:
            logger.error(f"Speech synthesis failed: {e}")
            raise
    
    async def create_voice_profile(
        self,
        profile_name: str,
        audio_sample: np.ndarray,
        sample_rate: int,
        user_id: str
    ) -> Dict:
        """
        Create a voice profile from an audio sample.
        
        Args:
            profile_name: Name for the voice profile
            audio_sample: Audio sample for voice cloning
            sample_rate: Sample rate of the audio
            user_id: User identifier
            
        Returns:
            Voice profile information
        """
        try:
            logger.info(f"Creating voice profile: {profile_name}")
            
            # Validate audio sample duration
            duration = len(audio_sample) / sample_rate
            if duration < DiaConfig.VOICE_CLONE_MIN_DURATION:
                raise ValueError(
                    f"Audio sample too short: {duration:.1f}s "
                    f"(minimum: {DiaConfig.VOICE_CLONE_MIN_DURATION}s)"
                )
            
            if duration > DiaConfig.VOICE_CLONE_MAX_DURATION:
                raise ValueError(
                    f"Audio sample too long: {duration:.1f}s "
                    f"(maximum: {DiaConfig.VOICE_CLONE_MAX_DURATION}s)"
                )
            
            # Process audio sample for voice cloning
            processed_audio = await self._process_voice_sample(
                audio_sample, sample_rate
            )
            
            # Extract voice features (placeholder implementation)
            voice_features = await self._extract_voice_features(processed_audio)
            
            # Create voice profile
            profile_id = f"{user_id}_{profile_name}"
            voice_profile = {
                "id": profile_id,
                "name": profile_name,
                "user_id": user_id,
                "features": voice_features,
                "duration": duration,
                "sample_rate": sample_rate,
                "created_at": time.time(),
                "quality_score": await self._assess_voice_quality(processed_audio)
            }
            
            # Store in cache
            self.voice_profiles[profile_id] = voice_profile
            
            logger.info(f"Voice profile created: {profile_name} ({duration:.1f}s)")
            
            return {
                "profile_id": profile_id,
                "name": profile_name,
                "duration": duration,
                "quality_score": voice_profile["quality_score"],
                "status": "ready"
            }
            
        except Exception as e:
            logger.error(f"Voice profile creation failed: {e}")
            raise
    
    async def generate_dialogue(
        self,
        dialogue_script: List[Dict],
        voice_profiles: Dict[str, str],
        adhd_mode: str = "conversational"
    ) -> Tuple[np.ndarray, int]:
        """
        Generate multi-speaker dialogue.
        
        Args:
            dialogue_script: List of dialogue entries with speaker and text
            voice_profiles: Mapping of speaker IDs to voice profiles
            adhd_mode: ADHD optimization mode
            
        Returns:
            Tuple of (audio_array, sample_rate)
        """
        try:
            logger.info(f"Generating dialogue with {len(dialogue_script)} turns")
            
            audio_segments = []
            
            for turn in dialogue_script:
                speaker_id = turn.get("speaker", "[S1]")
                text = turn.get("text", "")
                voice_profile = voice_profiles.get(speaker_id, "default")
                
                # Add speaker transition if needed
                if len(audio_segments) > 0:
                    silence = np.zeros(int(0.5 * DiaConfig.SAMPLE_RATE))
                    audio_segments.append(silence)
                
                # Generate speech for this turn
                audio, sample_rate = await self.synthesize_speech(
                    text=text,
                    voice_profile=voice_profile,
                    adhd_mode=adhd_mode,
                    speaker_id=speaker_id
                )
                
                audio_segments.append(audio)
            
            # Concatenate all segments
            full_audio = np.concatenate(audio_segments)
            
            logger.info(f"Dialogue generated: {len(full_audio)/sample_rate:.1f}s")
            
            return full_audio, sample_rate
            
        except Exception as e:
            logger.error(f"Dialogue generation failed: {e}")
            raise
    
    async def _prepare_text_for_adhd(
        self,
        text: str,
        adhd_mode: str,
        include_nonverbals: bool,
        speaker_id: str
    ) -> str:
        """Prepare text with ADHD-specific optimizations."""
        
        # Add speaker tag
        processed_text = f"{speaker_id} {text}"
        
        # Add nonverbals based on ADHD mode and context
        if include_nonverbals and ADHDConfig.ADHD_NONVERBAL_ENABLED:
            processed_text = await self._add_nonverbals(processed_text, adhd_mode)
        
        # Adjust for ADHD emotional state
        if adhd_mode in DiaConfig.ADHD_EMOTION_LEVELS:
            processed_text = await self._adjust_for_emotion(processed_text, adhd_mode)
        
        return processed_text
    
    async def _add_nonverbals(self, text: str, adhd_mode: str) -> str:
        """Add appropriate nonverbal sounds for ADHD engagement."""
        
        # Simple implementation - add nonverbals based on context
        if "great" in text.lower() or "excellent" in text.lower():
            return f"{text} (chuckles)"
        elif "difficult" in text.lower() or "hard" in text.lower():
            return f"(sighs) {text}"
        elif "break" in text.lower():
            return f"{text} (breathes)"
        
        return text
    
    async def _adjust_for_emotion(self, text: str, adhd_mode: str) -> str:
        """Adjust text for ADHD emotional state."""
        
        # Add emotional context markers (this would be more sophisticated)
        emotion_markers = {
            "calm": "",
            "excited": "!",
            "focused": ".",
            "overwhelmed": "...",
            "motivated": "!"
        }
        
        marker = emotion_markers.get(adhd_mode, "")
        if marker and not text.endswith(marker):
            text += marker
        
        return text
    
    def _get_adhd_generation_params(self, adhd_mode: str) -> Dict:
        """Get generation parameters optimized for ADHD mode."""
        
        base_params = {
            "temperature": DiaConfig.TEMPERATURE,
            "top_p": DiaConfig.TOP_P,
            "do_sample": DiaConfig.DO_SAMPLE
        }
        
        # Override with ADHD-specific parameters
        if adhd_mode in DiaConfig.ADHD_EMOTION_LEVELS:
            adhd_params = DiaConfig.ADHD_EMOTION_LEVELS[adhd_mode]
            base_params.update(adhd_params)
        
        return base_params
    
    async def _generate_audio(
        self,
        text: str,
        voice_profile: str,
        generation_params: Dict
    ) -> Tuple[np.ndarray, int]:
        """Generate audio using Dia model."""

        try:
            if DIA_AVAILABLE and hasattr(self.model, 'generate'):
                # Use real Dia model
                logger.debug(f"Generating audio with Dia model: {text[:50]}...")

                # Configure generation parameters
                use_torch_compile = generation_params.get('use_torch_compile', DiaConfig.USE_TORCH_COMPILE)
                verbose = generation_params.get('verbose', False)

                # Generate audio with Dia
                output = self.model.generate(
                    text,
                    use_torch_compile=use_torch_compile and self.device != "mps",
                    verbose=verbose
                )

                # Convert to numpy array if needed
                if torch.is_tensor(output):
                    audio = output.cpu().numpy()
                else:
                    audio = np.array(output)

                # Ensure proper shape and type
                if len(audio.shape) > 1:
                    audio = audio.squeeze()

                sample_rate = DiaConfig.SAMPLE_RATE

                logger.debug(f"Generated audio: {len(audio)} samples at {sample_rate}Hz")
                return audio.astype(np.float32), sample_rate

            else:
                # Fallback to mock implementation
                logger.warning("Using mock audio generation")
                return await self._generate_mock_audio(text)

        except Exception as e:
            logger.error(f"Audio generation failed: {e}")
            # Fallback to mock implementation
            return await self._generate_mock_audio(text)

    async def _generate_mock_audio(self, text: str) -> Tuple[np.ndarray, int]:
        """Generate mock audio for development/fallback."""
        # Simulate audio generation
        duration = len(text) * 0.1  # Rough estimate
        sample_rate = DiaConfig.SAMPLE_RATE
        samples = int(duration * sample_rate)

        # Generate placeholder audio (sine wave)
        t = np.linspace(0, duration, samples)
        frequency = 440  # A4 note
        audio = 0.1 * np.sin(2 * np.pi * frequency * t)

        return audio.astype(np.float32), sample_rate
    
    async def _post_process_audio(
        self,
        audio: np.ndarray,
        adhd_mode: str
    ) -> np.ndarray:
        """Post-process audio for ADHD optimization."""
        
        # Apply ADHD-specific audio processing
        if adhd_mode == "calm":
            # Slightly slower, more relaxed
            audio = self._adjust_tempo(audio, 0.95)
        elif adhd_mode == "excited":
            # Slightly faster, more energetic
            audio = self._adjust_tempo(audio, 1.05)
        elif adhd_mode == "overwhelmed":
            # Gentler, softer
            audio = audio * 0.8
        
        return audio
    
    def _adjust_tempo(self, audio: np.ndarray, factor: float) -> np.ndarray:
        """Adjust audio tempo by a factor."""
        # Simple tempo adjustment (in real implementation, use proper audio processing)
        if factor == 1.0:
            return audio
        
        # Resample to adjust tempo
        new_length = int(len(audio) / factor)
        indices = np.linspace(0, len(audio) - 1, new_length)
        return np.interp(indices, np.arange(len(audio)), audio)
    
    async def _process_voice_sample(
        self,
        audio: np.ndarray,
        sample_rate: int
    ) -> np.ndarray:
        """Process audio sample for voice cloning."""
        
        # Resample to Dia's native sample rate if needed
        if sample_rate != DiaConfig.VOICE_CLONE_SAMPLE_RATE:
            # Simple resampling (use proper audio processing in real implementation)
            ratio = DiaConfig.VOICE_CLONE_SAMPLE_RATE / sample_rate
            new_length = int(len(audio) * ratio)
            indices = np.linspace(0, len(audio) - 1, new_length)
            audio = np.interp(indices, np.arange(len(audio)), audio)
        
        # Normalize audio
        audio = audio / np.max(np.abs(audio))
        
        return audio
    
    async def _extract_voice_features(self, audio: np.ndarray) -> Dict:
        """Extract voice features for cloning."""

        try:
            if DIA_AVAILABLE and hasattr(self.model, 'extract_features'):
                # Use Dia's feature extraction if available
                features = self.model.extract_features(audio)
                return features
            else:
                # Fallback feature extraction
                return {
                    "pitch_mean": float(np.mean(audio)),
                    "pitch_std": float(np.std(audio)),
                    "energy": float(np.mean(audio ** 2)),
                    "spectral_features": audio[:100].tolist(),
                    "duration": len(audio) / DiaConfig.VOICE_CLONE_SAMPLE_RATE,
                    "sample_rate": DiaConfig.VOICE_CLONE_SAMPLE_RATE
                }
        except Exception as e:
            logger.warning(f"Feature extraction failed, using fallback: {e}")
            return {
                "pitch_mean": float(np.mean(audio)),
                "pitch_std": float(np.std(audio)),
                "energy": float(np.mean(audio ** 2)),
                "spectral_features": audio[:100].tolist(),
                "duration": len(audio) / DiaConfig.VOICE_CLONE_SAMPLE_RATE,
                "sample_rate": DiaConfig.VOICE_CLONE_SAMPLE_RATE
            }
    
    async def _assess_voice_quality(self, audio: np.ndarray) -> float:
        """Assess the quality of a voice sample."""
        
        # Simple quality assessment based on signal properties
        energy = np.mean(audio ** 2)
        snr_estimate = 20 * np.log10(energy / (np.std(audio) + 1e-8))
        
        # Normalize to 0-1 scale
        quality = min(max(snr_estimate / 20, 0), 1)
        
        return float(quality)
    
    def _get_text_length_bucket(self, length: int) -> str:
        """Get text length bucket for metrics."""
        if length < 50:
            return "short"
        elif length < 200:
            return "medium"
        elif length < 500:
            return "long"
        else:
            return "very_long"
    
    async def health_check(self) -> Dict:
        """Perform health check on the engine."""
        
        return {
            "status": "healthy" if self.is_initialized else "initializing",
            "model_loaded": self.model is not None,
            "device": self.device,
            "voice_profiles_count": len(self.voice_profiles),
            "generation_count": self.generation_count,
            "avg_generation_time": (
                self.total_generation_time / max(self.generation_count, 1)
            )
        }
    
    async def is_ready(self) -> bool:
        """Check if the engine is ready for requests."""
        return self.is_initialized and self.model is not None
    
    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up Dia engine...")
        
        # Clear voice profiles
        self.voice_profiles.clear()
        
        # Clear model from memory
        if self.model is not None:
            del self.model
            self.model = None
        
        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None
        
        # Clear CUDA cache if using GPU
        if self.device == "cuda" and torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.is_initialized = False
        logger.info("Dia engine cleanup completed")


# Mock classes for demonstration (replace with actual Dia imports)
class MockDiaModel:
    """Mock Dia model for demonstration."""
    
    def __init__(self, device):
        self.device = device
    
    def to(self, device):
        self.device = device
        return self


class MockDiaTokenizer:
    """Mock Dia tokenizer for demonstration."""

    def __init__(self):
        pass
