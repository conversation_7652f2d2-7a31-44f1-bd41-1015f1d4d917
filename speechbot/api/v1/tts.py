"""
TTS API Endpoints
ADHD-optimized text-to-speech with Dia TTS integration
"""

import asyncio
import io
import logging
from typing import Dict, Optional

import numpy as np
import soundfile as sf
from fastapi import APIRouter, Depends, HTTPException, Request, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from speechbot.services.dia_engine import DiaEngine
from speechbot.core.config import ADHDConfig

logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response Models
class TTSRequest(BaseModel):
    """Text-to-speech request model."""
    
    text: str = Field(
        ...,
        min_length=1,
        max_length=5000,
        description="Text to synthesize"
    )
    
    voice_profile: str = Field(
        default="default",
        description="Voice profile to use"
    )
    
    adhd_mode: str = Field(
        default="calm",
        pattern="^(calm|excited|focused|overwhelmed|motivated)$",
        description="ADHD emotional state for optimization"
    )
    
    include_nonverbals: bool = Field(
        default=True,
        description="Include nonverbal sounds (laughs, sighs, etc.)"
    )
    
    speaker_id: str = Field(
        default="[S1]",
        pattern="^\\[S[1-4]\\]$",
        description="Speaker identifier for dialogue"
    )
    
    format: str = Field(
        default="wav",
        pattern="^(wav|mp3|flac)$",
        description="Audio format"
    )


class TTSResponse(BaseModel):
    """Text-to-speech response model."""
    
    success: bool
    duration: float
    sample_rate: int
    format: str
    adhd_optimizations: Dict
    generation_time: float


class QuickTTSRequest(BaseModel):
    """Quick TTS request for simple use cases."""
    
    text: str = Field(..., max_length=500)
    voice: str = Field(default="default")
    mode: str = Field(default="calm")


def get_dia_engine(request: Request) -> DiaEngine:
    """Get Dia engine from application state."""
    if not hasattr(request.app.state, 'dia_engine'):
        raise HTTPException(
            status_code=503,
            detail="Dia TTS engine not available"
        )
    return request.app.state.dia_engine


@router.post(
    "/synthesize",
    response_model=TTSResponse,
    summary="Synthesize speech with ADHD optimizations",
    description="Convert text to speech with ADHD-specific adaptations and voice cloning"
)
async def synthesize_speech(
    request: TTSRequest,
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> Response:
    """
    Synthesize speech from text with ADHD optimizations.
    
    This endpoint provides comprehensive TTS with:
    - Voice cloning and personalization
    - ADHD emotional state adaptation
    - Nonverbal communication
    - Multi-speaker dialogue support
    """
    try:
        logger.info(f"TTS request: {len(request.text)} chars, mode: {request.adhd_mode}")
        
        # Generate speech
        audio_array, sample_rate = await dia_engine.synthesize_speech(
            text=request.text,
            voice_profile=request.voice_profile,
            adhd_mode=request.adhd_mode,
            include_nonverbals=request.include_nonverbals,
            speaker_id=request.speaker_id
        )
        
        # Convert to audio file
        audio_buffer = io.BytesIO()
        
        if request.format == "wav":
            sf.write(audio_buffer, audio_array, sample_rate, format='WAV')
            media_type = "audio/wav"
        elif request.format == "mp3":
            # For MP3, we'd need additional encoding (placeholder)
            sf.write(audio_buffer, audio_array, sample_rate, format='WAV')
            media_type = "audio/wav"  # Fallback to WAV
        else:
            sf.write(audio_buffer, audio_array, sample_rate, format='FLAC')
            media_type = "audio/flac"
        
        audio_buffer.seek(0)
        
        # Prepare response headers
        headers = {
            "X-Audio-Duration": str(len(audio_array) / sample_rate),
            "X-Sample-Rate": str(sample_rate),
            "X-ADHD-Mode": request.adhd_mode,
            "X-Voice-Profile": request.voice_profile,
            "X-Nonverbals-Included": str(request.include_nonverbals)
        }
        
        return StreamingResponse(
            io.BytesIO(audio_buffer.read()),
            media_type=media_type,
            headers=headers
        )
        
    except Exception as e:
        logger.error(f"TTS synthesis failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Speech synthesis failed: {str(e)}"
        )


@router.post(
    "/quick",
    summary="Quick text-to-speech",
    description="Simple TTS endpoint for basic use cases"
)
async def quick_tts(
    request: QuickTTSRequest,
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> Response:
    """
    Quick text-to-speech for simple use cases.
    
    Simplified endpoint with minimal configuration for
    rapid speech synthesis.
    """
    try:
        # Generate speech with default settings
        audio_array, sample_rate = await dia_engine.synthesize_speech(
            text=request.text,
            voice_profile=request.voice,
            adhd_mode=request.mode,
            include_nonverbals=True,
            speaker_id="[S1]"
        )
        
        # Convert to WAV
        audio_buffer = io.BytesIO()
        sf.write(audio_buffer, audio_array, sample_rate, format='WAV')
        audio_buffer.seek(0)
        
        return StreamingResponse(
            io.BytesIO(audio_buffer.read()),
            media_type="audio/wav"
        )
        
    except Exception as e:
        logger.error(f"Quick TTS failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Quick TTS failed: {str(e)}"
        )


@router.get(
    "/voices",
    summary="Get available voice profiles",
    description="List all available voice profiles and their characteristics"
)
async def get_voice_profiles(
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> Dict:
    """Get available voice profiles."""
    try:
        # Get voice profiles from engine
        profiles = dia_engine.voice_profiles
        
        # Format response
        voice_list = []
        for profile_id, profile_data in profiles.items():
            voice_list.append({
                "id": profile_id,
                "name": profile_data.get("name", "Unknown"),
                "user_id": profile_data.get("user_id", "system"),
                "quality_score": profile_data.get("quality_score", 0.0),
                "duration": profile_data.get("duration", 0.0),
                "created_at": profile_data.get("created_at", 0)
            })
        
        return {
            "voices": voice_list,
            "total_count": len(voice_list),
            "default_voices": ["default", "system"],
            "adhd_optimized": True
        }
        
    except Exception as e:
        logger.error(f"Failed to get voice profiles: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get voice profiles: {str(e)}"
        )


@router.get(
    "/adhd-modes",
    summary="Get ADHD optimization modes",
    description="List available ADHD emotional states and their characteristics"
)
async def get_adhd_modes() -> Dict:
    """Get available ADHD optimization modes."""
    
    modes = {
        "calm": {
            "description": "Relaxed, steady pace for focused work",
            "characteristics": ["slower tempo", "gentle tone", "minimal nonverbals"],
            "best_for": ["deep work", "reading", "concentration tasks"]
        },
        "excited": {
            "description": "Energetic, enthusiastic tone for motivation",
            "characteristics": ["faster tempo", "upbeat tone", "frequent nonverbals"],
            "best_for": ["celebrations", "motivation", "exercise"]
        },
        "focused": {
            "description": "Clear, direct communication for task focus",
            "characteristics": ["steady pace", "clear articulation", "structured delivery"],
            "best_for": ["instructions", "tutorials", "step-by-step guidance"]
        },
        "overwhelmed": {
            "description": "Gentle, supportive tone for difficult moments",
            "characteristics": ["slower pace", "softer volume", "comforting nonverbals"],
            "best_for": ["stress relief", "break reminders", "emotional support"]
        },
        "motivated": {
            "description": "Encouraging, confident tone for goal achievement",
            "characteristics": ["dynamic pace", "confident tone", "motivational nonverbals"],
            "best_for": ["goal setting", "progress updates", "achievement celebration"]
        }
    }
    
    return {
        "modes": modes,
        "default_mode": "calm",
        "adhd_optimized": True,
        "supports_transitions": True
    }


@router.get(
    "/capabilities",
    summary="Get TTS capabilities",
    description="Get information about Dia TTS capabilities and features"
)
async def get_capabilities(
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> Dict:
    """Get TTS engine capabilities."""
    
    return {
        "engine": "Dia TTS (Nari Labs)",
        "model_size": "1.6B parameters",
        "features": {
            "voice_cloning": {
                "enabled": True,
                "min_sample_duration": 5,
                "max_sample_duration": 15,
                "sample_rate": 24000
            },
            "dialogue_generation": {
                "enabled": True,
                "max_speakers": 4,
                "speaker_tags": ["[S1]", "[S2]", "[S3]", "[S4]"]
            },
            "nonverbal_communication": {
                "enabled": True,
                "types": ["laughs", "sighs", "gasps", "coughs", "whispers"]
            },
            "adhd_optimization": {
                "enabled": True,
                "modes": ["calm", "excited", "focused", "overwhelmed", "motivated"],
                "emotional_adaptation": True,
                "attention_management": True
            }
        },
        "audio_formats": ["wav", "mp3", "flac"],
        "sample_rates": [16000, 22050, 24000, 44100],
        "max_text_length": 5000,
        "real_time_factor": 2.0,
        "gpu_accelerated": True
    }


@router.get(
    "/health",
    summary="TTS engine health check",
    description="Check the health and status of the TTS engine"
)
async def tts_health_check(
    dia_engine: DiaEngine = Depends(get_dia_engine)
) -> Dict:
    """Check TTS engine health."""
    
    try:
        health_status = await dia_engine.health_check()
        
        return {
            "status": "healthy",
            "engine": health_status,
            "timestamp": asyncio.get_event_loop().time()
        }
        
    except Exception as e:
        logger.error(f"TTS health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"TTS engine unhealthy: {str(e)}"
        )
