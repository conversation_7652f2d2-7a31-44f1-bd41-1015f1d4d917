"""
Voice Profile Database Models
Persistent storage for ADHD-optimized voice profiles and user preferences
"""

from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

Base = declarative_base()


class VoiceProfile(Base):
    """Voice profile model for storing user voice cloning data."""
    
    __tablename__ = "voice_profiles"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User identification
    user_id = Column(String(255), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Voice characteristics
    duration = Column(Float, nullable=False)  # Sample duration in seconds
    quality_score = Column(Float, nullable=False)  # 0.0 to 1.0
    sample_rate = Column(Integer, nullable=False, default=24000)
    
    # Voice features (JSON storage for flexibility)
    features = Column(JSON, nullable=False)
    
    # File storage
    audio_file_path = Column(String(500), nullable=True)  # Path to original audio sample
    processed_file_path = Column(String(500), nullable=True)  # Path to processed audio
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_used_at = Column(DateTime, nullable=True)
    
    # Status and settings
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    usage_count = Column(Integer, default=0, nullable=False)
    
    # ADHD-specific settings
    preferred_adhd_modes = Column(JSON, nullable=True)  # List of preferred modes
    emotional_associations = Column(JSON, nullable=True)  # Emotional context mapping
    
    # Relationships
    synthesis_sessions = relationship("SynthesisSession", back_populates="voice_profile")
    
    def __repr__(self):
        return f"<VoiceProfile(id={self.id}, name='{self.name}', user_id='{self.user_id}')>"
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for API responses."""
        return {
            "id": str(self.id),
            "user_id": self.user_id,
            "name": self.name,
            "description": self.description,
            "duration": self.duration,
            "quality_score": self.quality_score,
            "sample_rate": self.sample_rate,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "usage_count": self.usage_count,
            "preferred_adhd_modes": self.preferred_adhd_modes,
            "emotional_associations": self.emotional_associations
        }


class SynthesisSession(Base):
    """Synthesis session model for tracking TTS usage and analytics."""
    
    __tablename__ = "synthesis_sessions"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User and voice profile
    user_id = Column(String(255), nullable=False, index=True)
    voice_profile_id = Column(UUID(as_uuid=True), ForeignKey("voice_profiles.id"), nullable=True)
    
    # Session details
    session_type = Column(String(50), nullable=False)  # tts, dialogue, body_doubling
    text_input = Column(Text, nullable=False)
    text_length = Column(Integer, nullable=False)
    
    # ADHD settings used
    adhd_mode = Column(String(50), nullable=False)
    include_nonverbals = Column(Boolean, default=True, nullable=False)
    speaker_configuration = Column(JSON, nullable=True)  # For dialogue sessions
    
    # Performance metrics
    generation_time = Column(Float, nullable=False)  # Time to generate audio
    audio_duration = Column(Float, nullable=False)  # Duration of generated audio
    realtime_factor = Column(Float, nullable=False)  # RTF calculation
    
    # Quality and user feedback
    quality_rating = Column(Float, nullable=True)  # User rating 1-5
    user_feedback = Column(Text, nullable=True)
    
    # Technical details
    model_version = Column(String(100), nullable=True)
    device_used = Column(String(50), nullable=True)  # cuda, cpu, mps
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    voice_profile = relationship("VoiceProfile", back_populates="synthesis_sessions")
    
    def __repr__(self):
        return f"<SynthesisSession(id={self.id}, type='{self.session_type}', user_id='{self.user_id}')>"
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for API responses."""
        return {
            "id": str(self.id),
            "user_id": self.user_id,
            "voice_profile_id": str(self.voice_profile_id) if self.voice_profile_id else None,
            "session_type": self.session_type,
            "text_length": self.text_length,
            "adhd_mode": self.adhd_mode,
            "include_nonverbals": self.include_nonverbals,
            "generation_time": self.generation_time,
            "audio_duration": self.audio_duration,
            "realtime_factor": self.realtime_factor,
            "quality_rating": self.quality_rating,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class UserPreferences(Base):
    """User preferences model for ADHD-specific settings."""
    
    __tablename__ = "user_preferences"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String(255), unique=True, nullable=False, index=True)
    
    # Default ADHD settings
    default_adhd_mode = Column(String(50), default="calm", nullable=False)
    default_voice_profile_id = Column(UUID(as_uuid=True), ForeignKey("voice_profiles.id"), nullable=True)
    
    # Nonverbal preferences
    enable_nonverbals = Column(Boolean, default=True, nullable=False)
    nonverbal_frequency = Column(Float, default=0.1, nullable=False)  # 0.0 to 1.0
    
    # Body doubling preferences
    default_encouragement_frequency = Column(String(20), default="medium", nullable=False)
    enable_break_reminders = Column(Boolean, default=True, nullable=False)
    
    # Audio preferences
    preferred_audio_format = Column(String(10), default="wav", nullable=False)
    preferred_sample_rate = Column(Integer, default=24000, nullable=False)
    
    # ADHD-specific customizations
    attention_span_minutes = Column(Integer, default=25, nullable=True)  # For session planning
    energy_patterns = Column(JSON, nullable=True)  # Time-of-day energy mapping
    trigger_words = Column(JSON, nullable=True)  # Words that cause issues
    comfort_phrases = Column(JSON, nullable=True)  # Phrases that help
    
    # Privacy settings
    allow_analytics = Column(Boolean, default=True, nullable=False)
    share_anonymous_data = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    default_voice_profile = relationship("VoiceProfile", foreign_keys=[default_voice_profile_id])
    
    def __repr__(self):
        return f"<UserPreferences(user_id='{self.user_id}', default_mode='{self.default_adhd_mode}')>"
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for API responses."""
        return {
            "id": str(self.id),
            "user_id": self.user_id,
            "default_adhd_mode": self.default_adhd_mode,
            "default_voice_profile_id": str(self.default_voice_profile_id) if self.default_voice_profile_id else None,
            "enable_nonverbals": self.enable_nonverbals,
            "nonverbal_frequency": self.nonverbal_frequency,
            "default_encouragement_frequency": self.default_encouragement_frequency,
            "enable_break_reminders": self.enable_break_reminders,
            "preferred_audio_format": self.preferred_audio_format,
            "preferred_sample_rate": self.preferred_sample_rate,
            "attention_span_minutes": self.attention_span_minutes,
            "energy_patterns": self.energy_patterns,
            "trigger_words": self.trigger_words,
            "comfort_phrases": self.comfort_phrases,
            "allow_analytics": self.allow_analytics,
            "share_anonymous_data": self.share_anonymous_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class DialogueTemplate(Base):
    """Dialogue template model for reusable conversation scenarios."""
    
    __tablename__ = "dialogue_templates"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Template identification
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(50), nullable=False)  # body_doubling, coaching, social_practice
    
    # Template content
    dialogue_script = Column(JSON, nullable=False)  # List of dialogue turns
    speaker_configuration = Column(JSON, nullable=False)  # Speaker setup
    
    # ADHD optimization
    recommended_adhd_mode = Column(String(50), nullable=False)
    target_duration_minutes = Column(Integer, nullable=True)
    difficulty_level = Column(String(20), default="beginner", nullable=False)
    
    # Usage and ratings
    usage_count = Column(Integer, default=0, nullable=False)
    average_rating = Column(Float, nullable=True)
    
    # Metadata
    created_by = Column(String(255), nullable=True)  # User who created it
    is_public = Column(Boolean, default=False, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)  # Reviewed by experts
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<DialogueTemplate(id={self.id}, name='{self.name}', category='{self.category}')>"
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for API responses."""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "dialogue_script": self.dialogue_script,
            "speaker_configuration": self.speaker_configuration,
            "recommended_adhd_mode": self.recommended_adhd_mode,
            "target_duration_minutes": self.target_duration_minutes,
            "difficulty_level": self.difficulty_level,
            "usage_count": self.usage_count,
            "average_rating": self.average_rating,
            "is_public": self.is_public,
            "is_verified": self.is_verified,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
