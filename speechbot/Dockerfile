# Speechbot Dockerfile with Dia TTS Integration
# Optimized for ADHD voice synthesis with 1.6B parameter model

# Use Python base image - will install PyTorch ROCm for AMD GPU support
FROM python:3.10-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0

# Install system dependencies (AMD GPU support via PyTorch ROCm)
RUN apt-get update && apt-get install -y \
    python3-dev \
    python3-pip \
    git \
    wget \
    curl \
    ffmpeg \
    libsndfile1 \
    libsox-dev \
    sox \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Install PyTorch with ROCm support FIRST (before requirements.txt)
RUN pip3 install --no-cache-dir \
    torch==2.3.1+rocm5.7 \
    torchaudio==2.3.1+rocm5.7 \
    --index-url https://download.pytorch.org/whl/rocm5.7

# Copy requirements and install (excluding torch/torchaudio)
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# Install Dia TTS dependencies
RUN pip3 install --no-cache-dir \
    transformers==4.35.0 \
    accelerate==0.24.0 \
    datasets==2.14.0 \
    librosa==0.10.1 \
    soundfile==0.12.1 \
    numpy==1.24.3 \
    scipy==1.11.3 \
    matplotlib==3.7.2 \
    tensorboard==2.14.0 \
    wandb==0.15.12

# Install Dia TTS from GitHub
RUN pip3 install --no-cache-dir git+https://github.com/nari-labs/dia.git

# Set up Hugging Face token for model downloads
ARG HF_TOKEN
ENV HF_TOKEN=${HF_TOKEN}

# Copy application code
WORKDIR /app
COPY speechbot/ ./speechbot/
COPY app/core/ ./app/core/
COPY app/models/ ./app/models/

# Create directories for models and cache
RUN mkdir -p /app/models/dia /app/cache /app/audio_samples

# Pre-download Dia model (optional - can be done at runtime)
# Uncomment to pre-download model during build (requires HF_TOKEN)
# RUN python3 -c "from dia.model import Dia; Dia.from_pretrained('nari-labs/Dia-1.6B')"

# Set up environment
ENV PYTHONPATH=/app:/app/dia
ENV DIA_MODEL_PATH=/app/models/dia
ENV DIA_CACHE_PATH=/app/cache
ENV AUDIO_SAMPLES_PATH=/app/audio_samples

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run the application
CMD ["python3", "-m", "speechbot.main"]
