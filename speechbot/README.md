# Speechbot: ADHD-Optimized Voice Assistant

Speechbot is Project Chronos's revolutionary voice assistant powered by Nari Labs' Dia TTS (1.6B parameters), specifically designed to support ADHD users with natural, engaging, and therapeutically beneficial voice interactions.

## 🎯 Features

### 🎭 Voice Cloning & Personalization
- Create personalized voices from just **5-10 second audio samples**
- Rapid setup reduces onboarding friction for ADHD users
- Multiple voice profiles for different contexts and moods
- Automatic quality assessment and optimization

### 💬 Multi-Speaker Dialogue Generation
- Realistic conversations between up to **4 speakers**
- Virtual body doubling companions for accountability
- Social practice scenarios in a safe environment
- Therapeutic dialogue for emotional support

### 🧠 ADHD-Optimized Emotional Support
- **5 emotional modes**: calm, excited, focused, overwhelmed, motivated
- Adaptive speech rate and tone based on cognitive state
- Nonverbal communication (laughs, sighs, gasps) for natural interaction
- Executive function support through structured dialogue

### 🎯 Therapeutic Applications
- Task coaching with step-by-step guidance
- Motivation support during difficult moments
- Break reminders with gentle, encouraging tone
- Self-compassion modeling through personalized voices

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- NVIDIA GPU with 8GB+ VRAM (recommended)
- Hugging Face account and token

### Installation

1. **Clone and setup**:
   ```bash
   cd speechbot/
   python setup_dia.py
   ```

2. **Configure environment**:
   ```bash
   # Edit .env file and add your HF_TOKEN
   nano .env
   ```

3. **Test installation**:
   ```bash
   python test_dia_integration.py
   ```

4. **Start service**:
   ```bash
   python main.py
   ```

### Docker Setup

1. **Set Hugging Face token**:
   ```bash
   echo "HF_TOKEN=your_token_here" >> .env
   ```

2. **Build and run**:
   ```bash
   docker-compose up speechbot
   ```

## 📡 API Usage

### Basic Text-to-Speech

```bash
curl -X POST "http://localhost:8001/api/v1/tts/quick" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "Hello, this is ADHD-optimized speech synthesis!",
       "voice": "default",
       "mode": "calm"
     }' \
     --output speech.wav
```

### Voice Profile Creation

```bash
curl -X POST "http://localhost:8001/api/v1/voice-profiles/create" \
     -F "name=My Voice" \
     -F "user_id=user123" \
     -F "audio_file=@my_voice_sample.wav"
```

### Dialogue Generation

```bash
curl -X POST "http://localhost:8001/api/v1/dialogue/generate" \
     -H "Content-Type: application/json" \
     -d '{
       "dialogue": [
         {"speaker": "[S1]", "text": "I need help staying focused today."},
         {"speaker": "[S2]", "text": "I am here to support you! What are you working on?"}
       ],
       "voice_profiles": {"[S1]": "my_voice", "[S2]": "default"},
       "adhd_mode": "supportive"
     }' \
     --output dialogue.wav
```

### Body Doubling Session

```bash
curl -X POST "http://localhost:8001/api/v1/dialogue/body-doubling" \
     -H "Content-Type: application/json" \
     -d '{
       "user_task": "Writing project proposal",
       "session_duration": 25,
       "encouragement_frequency": "medium"
     }' \
     --output body_doubling.wav
```

## 🧠 ADHD Features

### Emotional Modes

- **Calm**: Slower tempo, gentle tone for focused work
- **Excited**: Faster pace, upbeat tone for motivation  
- **Focused**: Clear articulation, structured delivery
- **Overwhelmed**: Softer volume, comforting nonverbals
- **Motivated**: Dynamic pace, confident tone

### Nonverbal Communication

- `(laughs)` - Joy, celebration, humor
- `(sighs)` - Understanding, empathy, patience
- `(gasps)` - Surprise, realization, excitement
- `(gentle breath)` - Calming, centering, presence
- `(whispers)` - Intimacy, confidentiality, care

### Voice Cloning Tips

**Recording Guidelines**:
- 8-12 seconds optimal duration
- Quiet environment with minimal echo
- Natural, conversational tone
- Include varied intonation and emotion

**Sample Text**:
```
"Hello, my name is [Your Name]. I'm creating a voice profile for my ADHD 
productivity assistant. The quick brown fox jumps over the lazy dog. 
I enjoy using technology to help manage my daily tasks and stay organized."
```

## 🏗️ Architecture

### Core Components

- **DiaEngine**: Core TTS integration with Dia 1.6B model
- **Voice Profiles**: User voice cloning and management
- **Dialogue Generator**: Multi-speaker conversation creation
- **ADHD Optimizer**: Emotional state adaptation
- **API Layer**: RESTful endpoints for all features

### Performance

- **Real-time Factor**: 2x on RTX 4090
- **Model Size**: 1.6B parameters
- **Sample Rate**: 24kHz native
- **Concurrent Users**: Up to 10 simultaneous requests
- **Voice Profiles**: 50 per user maximum

## 🔧 Configuration

### Environment Variables

```bash
# Required
HF_TOKEN=your_huggingface_token

# Dia Configuration
DIA_DEVICE=cuda                    # cuda, cpu, mps
DIA_COMPUTE_DTYPE=float16         # float16, float32
DIA_USE_TORCH_COMPILE=true        # Performance optimization
DIA_SAMPLE_RATE=24000             # Audio sample rate

# ADHD Features
ADHD_EMOTION_ADAPTATION=true      # Enable emotional adaptation
ADHD_NONVERBAL_ENABLED=true       # Enable nonverbal sounds
ADHD_DIALOGUE_MODE=true           # Enable dialogue features

# Performance
MAX_CONCURRENT_REQUESTS=10        # Concurrent synthesis limit
REQUEST_TIMEOUT=30                # Request timeout (seconds)
MODEL_CACHE_SIZE=3                # Model cache size
```

### Hardware Requirements

**Minimum**:
- GPU: 8GB VRAM (RTX 3070 or equivalent)
- CPU: 8-core processor
- RAM: 16GB system memory
- Storage: 50GB for models and cache

**Recommended**:
- GPU: 10GB+ VRAM (RTX 4080/4090)
- CPU: 12+ core processor
- RAM: 32GB system memory
- Storage: 100GB SSD for optimal performance

## 🧪 Testing

### Run All Tests

```bash
python test_dia_integration.py
```

### Individual Test Categories

```bash
# Basic synthesis
python -c "import asyncio; from test_dia_integration import test_basic_synthesis, test_dia_initialization; asyncio.run(test_basic_synthesis(asyncio.run(test_dia_initialization())))"

# ADHD modes
python -c "import asyncio; from test_dia_integration import test_adhd_modes, test_dia_initialization; asyncio.run(test_adhd_modes(asyncio.run(test_dia_initialization())))"

# Performance benchmarks
python -c "import asyncio; from test_dia_integration import test_performance_benchmarks, test_dia_initialization; asyncio.run(test_performance_benchmarks(asyncio.run(test_dia_initialization())))"
```

## 📚 Documentation

- **Getting Started**: `../docs/speechbot/getting-started.rst`
- **Voice Cloning Guide**: `../docs/speechbot/voice-cloning.rst`
- **Dialogue Generation**: `../docs/speechbot/dialogue-generation.rst`
- **ADHD Features**: `../docs/speechbot/adhd-features.rst`
- **API Reference**: `../docs/speechbot/api-reference.rst`

## 🐛 Troubleshooting

### Common Issues

**"Dia TTS not available" Error**:
```bash
pip install git+https://github.com/nari-labs/dia.git
```

**Model Download Fails**:
- Verify HF_TOKEN is set correctly
- Check internet connectivity
- Ensure sufficient disk space (10GB+)

**GPU Out of Memory**:
- Reduce batch size in config
- Use float16 instead of float32
- Close other GPU applications

**Slow Generation**:
- Ensure GPU is being used (`DIA_DEVICE=cuda`)
- Enable torch.compile (`DIA_USE_TORCH_COMPILE=true`)
- Check GPU utilization with `nvidia-smi`

### Performance Optimization

1. **Enable torch.compile**:
   ```bash
   export DIA_USE_TORCH_COMPILE=true
   ```

2. **Use appropriate precision**:
   ```bash
   export DIA_COMPUTE_DTYPE=float16  # Faster, less memory
   export DIA_COMPUTE_DTYPE=float32  # Higher quality, more memory
   ```

3. **Optimize batch processing**:
   ```bash
   export MAX_CONCURRENT_REQUESTS=5  # Reduce for lower memory usage
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is part of Project Chronos and follows the same licensing terms.

## 🆘 Support

- **Documentation**: Check `docs/speechbot/` for comprehensive guides
- **Issues**: Report bugs and feature requests on GitHub
- **Community**: Join the ADHD productivity community discussions
- **Nari Labs**: Visit [Nari Labs Discord](https://discord.gg/bJq6vjRRKv) for Dia TTS support

## 🎉 Acknowledgments

- **Nari Labs** for the incredible Dia TTS model
- **ADHD Community** for feedback and feature requests
- **Project Chronos Team** for the comprehensive productivity platform
- **Contributors** who helped make this vision a reality

---

**🎭 Transform your ADHD productivity journey with the power of personalized voice assistance!**
