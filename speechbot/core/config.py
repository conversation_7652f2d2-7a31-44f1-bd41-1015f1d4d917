"""
Speechbot Configuration
Settings for ADHD-optimized voice synthesis with Dia TTS
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "Speechbot"
    DEBUG: bool = False
    VERSION: str = "1.0.0"
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8001
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # Database
    DATABASE_URL: str = "postgresql+asyncpg://chronos:chronos_pass@postgres:5432/chronos"
    
    # Redis
    REDIS_URL: str = "redis://redis:6379/2"  # Use database 2 for speechbot
    
    # Dia TTS Configuration
    DIA_MODEL_NAME: str = "nari-labs/Dia-1.6B"
    DIA_MODEL_PATH: str = "/app/models/dia"
    DIA_CACHE_PATH: str = "/app/cache"
    DIA_DEVICE: str = "cuda"  # or "cpu"
    DIA_COMPUTE_DTYPE: str = "float16"
    DIA_USE_TORCH_COMPILE: bool = True
    DIA_SAMPLE_RATE: int = 24000
    DIA_MAX_LENGTH: int = 1024
    DIA_BATCH_SIZE: int = 1
    DIA_TEMPERATURE: float = 0.8
    DIA_TOP_P: float = 0.9
    
    # Voice Cloning
    VOICE_CLONE_DURATION: int = 10  # seconds
    VOICE_SAMPLES_PATH: str = "/app/audio_samples"
    MAX_VOICE_PROFILES: int = 50  # per user
    
    # ADHD Features
    ADHD_EMOTION_ADAPTATION: bool = True
    ADHD_NONVERBAL_ENABLED: bool = True
    ADHD_DIALOGUE_MODE: bool = True
    
    # Audio Processing
    AUDIO_FORMAT: str = "wav"
    AUDIO_QUALITY: str = "high"  # high, medium, low
    MAX_AUDIO_LENGTH: int = 300  # seconds
    
    # Performance
    MAX_CONCURRENT_REQUESTS: int = 10
    REQUEST_TIMEOUT: int = 30  # seconds
    MODEL_CACHE_SIZE: int = 3
    
    # Monitoring
    METRICS_ENABLED: bool = True
    LOG_LEVEL: str = "INFO"
    
    # Security
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # File Storage
    UPLOAD_MAX_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_AUDIO_FORMATS: List[str] = ["wav", "mp3", "flac", "ogg"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Dia-specific configuration
class DiaConfig:
    """Dia TTS engine configuration."""
    
    # Model settings
    MODEL_NAME = "nari-labs/Dia-1.6B"
    COMPUTE_DTYPE = "float16"
    USE_TORCH_COMPILE = True
    
    # Audio settings
    SAMPLE_RATE = 24000
    MAX_LENGTH = 1024
    BATCH_SIZE = 1
    
    # Generation settings
    TEMPERATURE = 0.8
    TOP_P = 0.9
    DO_SAMPLE = True
    
    # Speaker and nonverbal tags
    SPEAKER_TAGS = ["[S1]", "[S2]", "[S3]", "[S4]"]
    NONVERBAL_TAGS = [
        "(laughs)", "(chuckles)", "(giggles)",
        "(sighs)", "(gasps)", "(whispers)",
        "(coughs)", "(clears throat)",
        "(pauses)", "(breathes)"
    ]
    
    # ADHD-specific settings
    ADHD_EMOTION_LEVELS = {
        "calm": {"temperature": 0.6, "top_p": 0.8},
        "excited": {"temperature": 1.0, "top_p": 0.9},
        "focused": {"temperature": 0.7, "top_p": 0.85},
        "overwhelmed": {"temperature": 0.5, "top_p": 0.7},
        "motivated": {"temperature": 0.9, "top_p": 0.9}
    }
    
    # Voice cloning settings
    VOICE_CLONE_MIN_DURATION = 5  # seconds
    VOICE_CLONE_MAX_DURATION = 15  # seconds
    VOICE_CLONE_SAMPLE_RATE = 24000
    
    # Performance settings
    MAX_BATCH_SIZE = 4
    MAX_SEQUENCE_LENGTH = 2048
    MEMORY_EFFICIENT = True


# ADHD-specific configuration
class ADHDConfig:
    """ADHD optimization settings."""
    
    # Attention and engagement
    ATTENTION_SPAN_ADAPTATION = True
    ENGAGEMENT_VARIETY = True
    NONVERBAL_FREQUENCY = 0.1  # 10% of responses include nonverbals
    
    # Emotional support
    EMOTIONAL_TONE_MATCHING = True
    ENCOURAGEMENT_FREQUENCY = 0.2  # 20% of responses include encouragement
    PATIENCE_LEVEL = "high"  # high, medium, low
    
    # Cognitive load management
    SPEECH_RATE_ADAPTATION = True
    COMPLEXITY_ADJUSTMENT = True
    BREAK_REMINDERS = True
    
    # Voice variety
    SPEAKER_ROTATION = True
    VOICE_PERSONALITY_MATCHING = True
    
    # Session types and their voice characteristics
    SESSION_VOICE_PROFILES = {
        "pomodoro": {
            "tone": "focused",
            "pace": "steady",
            "encouragement": "structured"
        },
        "deep_work": {
            "tone": "calm",
            "pace": "slow",
            "encouragement": "minimal"
        },
        "micro_focus": {
            "tone": "gentle",
            "pace": "relaxed",
            "encouragement": "frequent"
        },
        "body_doubling": {
            "tone": "social",
            "pace": "conversational",
            "encouragement": "collaborative"
        }
    }


# Create settings instance
settings = Settings()

# Validate GPU availability
def check_gpu_availability():
    """Check if GPU is available for Dia TTS."""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            return {
                "available": True,
                "count": gpu_count,
                "name": gpu_name,
                "memory": torch.cuda.get_device_properties(0).total_memory if gpu_count > 0 else 0
            }
        else:
            return {"available": False, "reason": "CUDA not available"}
    except ImportError:
        return {"available": False, "reason": "PyTorch not installed"}


# Export configurations
__all__ = ["settings", "DiaConfig", "ADHDConfig", "check_gpu_availability"]
