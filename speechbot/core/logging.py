"""
Logging configuration for Speechbot
Structured logging with ADHD-specific context
"""

import logging
import sys
from typing import Dict, Any

import structlog
from speechbot.core.config import settings


def setup_logging():
    """Setup structured logging for Speechbot."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper())
    )
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("speechbot").setLevel(logging.DEBUG if settings.DEBUG else logging.INFO)


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


class ADHDContextLogger:
    """Logger with ADHD-specific context tracking."""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
        self.context = {}
    
    def add_adhd_context(self, **kwargs):
        """Add ADHD-specific context to logs."""
        adhd_context = {
            f"adhd_{key}": value for key, value in kwargs.items()
        }
        self.context.update(adhd_context)
    
    def log_tts_request(self, text_length: int, voice_profile: str, adhd_mode: str):
        """Log TTS request with ADHD context."""
        self.logger.info(
            "TTS request processed",
            text_length=text_length,
            voice_profile=voice_profile,
            adhd_mode=adhd_mode,
            **self.context
        )
    
    def log_voice_profile_creation(self, profile_name: str, duration: float, quality: float):
        """Log voice profile creation."""
        self.logger.info(
            "Voice profile created",
            profile_name=profile_name,
            duration=duration,
            quality_score=quality,
            **self.context
        )
    
    def log_dialogue_generation(self, turns: int, speakers: int, duration: float):
        """Log dialogue generation."""
        self.logger.info(
            "Dialogue generated",
            dialogue_turns=turns,
            speaker_count=speakers,
            audio_duration=duration,
            **self.context
        )
    
    def log_error(self, error: Exception, operation: str):
        """Log error with context."""
        self.logger.error(
            f"Operation failed: {operation}",
            error_type=type(error).__name__,
            error_message=str(error),
            operation=operation,
            **self.context,
            exc_info=True
        )
