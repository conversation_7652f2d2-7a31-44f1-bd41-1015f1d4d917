version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: chronos-postgres
    environment:
      POSTGRES_USER: chronos
      POSTGRES_PASSWORD: chronos_dev
      POSTGRES_DB: chronos
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - chronos-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chronos -d chronos"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: chronos-redis
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chronos-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Chronos Backend API (FastAPI)
  chronos-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: chronos-api
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - DATABASE_URL=postgresql+asyncpg://chronos:chronos_dev@postgres:5432/chronos
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
      - CORS_ORIGINS=http://localhost:3000,http://chronos-ui.autism.localhost
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    networks:
      - chronos-network
    command: poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.chronos-api.rule=Host(`api.autism.localhost`)"
      - "traefik.http.routers.chronos-api.entrypoints=web"
      - "traefik.http.services.chronos-api.loadbalancer.server.port=8000"

  # Chronos Frontend UI (Next.js)
  chronos-ui:
    build:
      context: ./chronos-ui
      dockerfile: Dockerfile
    container_name: chronos-ui
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
      - NODE_ENV=development
    volumes:
      - ./chronos-ui:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - chronos-api
    networks:
      - chronos-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.chronos-ui.rule=Host(`chronos.autism.localhost`)"
      - "traefik.http.routers.chronos-ui.entrypoints=web"
      - "traefik.http.services.chronos-ui.loadbalancer.server.port=3000"

  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: chronos-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--log.level=INFO"
    ports:
      - "80:80"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - chronos-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.autism.localhost`)"
      - "traefik.http.routers.traefik.entrypoints=web"
      - "traefik.http.services.traefik.loadbalancer.server.port=8080"

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: chronos-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - chronos-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.autism.localhost`)"
      - "traefik.http.routers.prometheus.entrypoints=web"

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: chronos-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - chronos-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.autism.localhost`)"
      - "traefik.http.routers.grafana.entrypoints=web"

  # Mailhog (Email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: chronos-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - chronos-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mailhog.rule=Host(`mail.autism.localhost`)"
      - "traefik.http.routers.mailhog.entrypoints=web"
      - "traefik.http.services.mailhog.loadbalancer.server.port=8025"

  # MinIO (S3-compatible storage for voice files)
  minio:
    image: minio/minio:latest
    container_name: chronos-minio
    command: server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=chronos
      - MINIO_ROOT_PASSWORD=chronos123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - chronos-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio.rule=Host(`minio.autism.localhost`)"
      - "traefik.http.routers.minio.entrypoints=web"
      - "traefik.http.services.minio.loadbalancer.server.port=9001"

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  minio_data:

networks:
  chronos-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
