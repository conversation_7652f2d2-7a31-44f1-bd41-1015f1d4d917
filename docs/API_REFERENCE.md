# 📡 Project Chronos + Speechbot API Reference

**Complete API Documentation for ADHD Voice Assistant Platform**

## 🌟 Overview

The Project Chronos + Speechbot API provides comprehensive endpoints for ADHD-optimized voice synthesis, emotion detection, body doubling, and user management. All endpoints are designed with ADHD users in mind, featuring clear responses, helpful error messages, and accessibility considerations.

## 🔐 Authentication

### Base URL
```
Production: https://api.your-domain.com
Development: http://api.autism.localhost:8090
```

### Authentication Methods

**Bearer Token Authentication:**
```http
Authorization: Bearer <access_token>
```

**API Key Authentication (for integrations):**
```http
X-API-Key: <api_key>
```

### Authentication Endpoints

#### Register User
```http
POST /api/v1/auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "full_name": "<PERSON>",
  "adhd_type": "combined",
  "adhd_severity": "moderate",
  "preferred_modes": ["calm", "focused"],
  "accessibility_needs": ["high_contrast", "large_text"]
}
```

**Response (201 Created):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "adhd_type": "combined",
  "adhd_severity": "moderate",
  "is_active": true,
  "created_at": "2024-01-15T10:30:00Z"
}
```

#### Login
```http
POST /api/v1/auth/login
```

**Request Body (Form Data):**
```
username=<EMAIL>
password=securepassword123
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

#### Refresh Token
```http
POST /api/v1/auth/refresh
```

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## 🎭 Speechbot Voice Synthesis

### Quick Synthesis
```http
POST /api/v1/speechbot/tts/quick
```

**Description:** Generate speech quickly with minimal configuration. Perfect for testing and simple use cases.

**Request Body:**
```json
{
  "text": "Hello, this is a test of the ADHD voice assistant!",
  "mode": "calm",
  "voice": "default"
}
```

**Response Headers:**
```
Content-Type: audio/wav
X-Audio-Duration: 3.2
X-Sample-Rate: 24000
X-ADHD-Mode: calm
X-Generation-Time: 1.8
```

**Response Body:** Binary audio data (WAV format)

### Advanced Synthesis
```http
POST /api/v1/speechbot/tts/synthesize
```

**Description:** Full-featured speech synthesis with all ADHD optimization options.

**Request Body:**
```json
{
  "text": "Today is a new opportunity to shine. You have unique strengths that the world needs.",
  "voice_profile": "user-voice-123",
  "adhd_mode": "motivated",
  "include_nonverbals": true,
  "speed_adjustment": 1.0,
  "pitch_adjustment": 0.0,
  "format": "wav",
  "sample_rate": 24000,
  "user_context": {
    "session_type": "morning_affirmation",
    "emotional_state": "motivated"
  }
}
```

**Response Headers:**
```
Content-Type: audio/wav
X-Audio-Duration: 5.7
X-Sample-Rate: 24000
X-ADHD-Mode: motivated
X-Voice-Profile: user-voice-123
X-Nonverbals-Included: true
X-Generation-Time: 2.3
X-Quality-Score: 0.92
```

### ADHD Modes

| Mode | Description | Use Cases | Voice Characteristics |
|------|-------------|-----------|----------------------|
| `calm` | Relaxed, steady pace | Overwhelm, anxiety, bedtime | Slower pace, lower pitch, gentle |
| `excited` | Energetic, upbeat | Motivation, celebration | Faster pace, higher energy |
| `focused` | Clear, direct | Work tasks, concentration | Clear articulation, steady rhythm |
| `overwhelmed` | Gentle, supportive | Stress, difficult moments | Very gentle, slower, nurturing |
| `motivated` | Encouraging, confident | Goals, achievements | Strong, empowering tone |

## ⚡ Real-Time Streaming

### Start Streaming Synthesis
```http
POST /api/v1/speechbot/streaming/synthesize
```

**Description:** Generate audio in real-time chunks for immediate playback. Reduces waiting anxiety for ADHD users.

**Request Body:**
```json
{
  "text": "This is a longer text that will be streamed in real-time as it's being generated. Perfect for ADHD users who need immediate feedback.",
  "voice_profile": "default",
  "adhd_mode": "calm",
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "include_nonverbals": true,
  "auto_emotion_detection": true,
  "stream_id": "stream_123456"
}
```

**Response:** Multipart streaming response with audio chunks

**Response Headers:**
```
Content-Type: multipart/x-mixed-replace; boundary=chunk
X-Streaming: true
X-ADHD-Mode: calm
Cache-Control: no-cache
Connection: keep-alive
```

**Chunk Format:**
```
--chunk-0
Content-Type: audio/wav
X-Chunk-Index: 0
X-Sample-Rate: 24000
X-Is-Final: false
X-Metadata: {"text_chunk": "This is a longer text", "chunk_duration": 1.2}
Content-Length: 57600

[Binary audio data]

--chunk-1
Content-Type: audio/wav
X-Chunk-Index: 1
X-Sample-Rate: 24000
X-Is-Final: false
...
```

### Stream Events (Server-Sent Events)
```http
GET /api/v1/speechbot/streaming/events/{stream_id}
```

**Description:** Real-time progress updates for streaming synthesis.

**Response (text/event-stream):**
```
event: stream_progress
data: {"stream_id": "stream_123456", "status": "active", "chunks_sent": 5, "total_duration": 3.2, "elapsed_time": 2.1}

event: stream_progress
data: {"stream_id": "stream_123456", "status": "active", "chunks_sent": 10, "total_duration": 6.8, "elapsed_time": 4.3}

event: stream_complete
data: {"stream_id": "stream_123456", "status": "complete", "total_chunks": 12, "final_duration": 8.1}
```

### Stop Streaming
```http
DELETE /api/v1/speechbot/streaming/streams/{stream_id}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Stream stream_123456 stopped successfully"
}
```

## 🧠 Emotion Detection

### Analyze Text Emotion
```http
POST /api/v1/speechbot/streaming/emotion/analyze
```

**Description:** Detect emotional content in text and suggest optimal ADHD mode.

**Request Body:**
```json
{
  "text": "I'm feeling overwhelmed with all these tasks and deadlines. Everything feels like too much right now.",
  "user_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Response (200 OK):**
```json
{
  "emotion_scores": {
    "overwhelmed": 0.85,
    "calm": 0.05,
    "excited": 0.02,
    "focused": 0.03,
    "motivated": 0.05
  },
  "suggested_adhd_mode": "overwhelmed",
  "confidence": 0.85,
  "insights": {
    "primary_emotion": "overwhelmed",
    "emotion_scores": {
      "overwhelmed": 0.85,
      "calm": 0.05,
      "excited": 0.02,
      "focused": 0.03,
      "motivated": 0.05
    },
    "recommendations": [
      "Consider using 'calm' mode for soothing speech",
      "Take breaks between tasks",
      "Try breaking large tasks into smaller steps"
    ],
    "suggested_actions": [
      "Start a body doubling session",
      "Use breathing exercises",
      "Create a simple task list"
    ]
  }
}
```

### Get Emotion Keywords
```http
GET /api/v1/speechbot/streaming/emotion/keywords
```

**Description:** Get the keywords used for emotion detection (for transparency).

**Response (200 OK):**
```json
{
  "emotion_keywords": {
    "overwhelmed": [
      "too much", "can't handle", "overwhelmed", "stressed", "anxious",
      "panic", "chaos", "scattered", "frazzled", "drowning"
    ],
    "focused": [
      "concentrate", "focus", "attention", "clear", "organized",
      "systematic", "methodical", "structured", "planned"
    ],
    "excited": [
      "excited", "energetic", "motivated", "enthusiastic", "pumped",
      "ready", "eager", "passionate", "inspired", "driven"
    ],
    "calm": [
      "calm", "peaceful", "relaxed", "steady", "balanced",
      "centered", "composed", "tranquil", "serene"
    ],
    "motivated": [
      "motivated", "determined", "confident", "capable", "strong",
      "accomplished", "successful", "proud", "achieving"
    ]
  },
  "description": "Keywords used for automatic emotion detection from text",
  "note": "These keywords help determine the optimal ADHD mode for synthesis"
}
```

## 👤 Voice Profiles

### List Voice Profiles
```http
GET /api/v1/speechbot/voice-profiles/list
```

**Response (200 OK):**
```json
[
  {
    "profile_id": "profile-123",
    "name": "My Calm Voice",
    "description": "Personal voice for self-compassion",
    "quality_score": 0.92,
    "duration": 8.5,
    "status": "ready",
    "usage_count": 47,
    "created_at": "2024-01-10T14:30:00Z",
    "last_used": "2024-01-15T09:15:00Z"
  },
  {
    "profile_id": "profile-456",
    "name": "Confident Me",
    "description": "Voice for motivation and goals",
    "quality_score": 0.88,
    "duration": 10.2,
    "status": "ready",
    "usage_count": 23,
    "created_at": "2024-01-12T16:45:00Z",
    "last_used": "2024-01-14T11:30:00Z"
  }
]
```

### Create Voice Profile
```http
POST /api/v1/speechbot/voice-profiles/create
```

**Request (multipart/form-data):**
```
name: "My Supportive Voice"
description: "Voice for daily affirmations"
audio_file: [Binary audio file - WAV, MP3, FLAC, M4A, or OGG]
```

**Response (200 OK):**
```json
{
  "profile_id": "profile-789",
  "name": "My Supportive Voice",
  "description": "Voice for daily affirmations",
  "status": "processing",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "quality_assessment": {
    "duration": 9.3,
    "sample_rate": 44100,
    "signal_quality": "good",
    "background_noise": "minimal",
    "recommendations": [
      "Audio quality is excellent for voice cloning",
      "Duration is optimal for training"
    ]
  }
}
```

### Test Voice Profile
```http
POST /api/v1/speechbot/voice-profiles/{profile_id}/test
```

**Request Body:**
```json
{
  "text": "This is a test of my personal voice profile for self-compassion.",
  "adhd_mode": "calm"
}
```

**Response:** Audio stream with test synthesis

### Delete Voice Profile
```http
DELETE /api/v1/speechbot/voice-profiles/{profile_id}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Voice profile deleted successfully",
  "profile_id": "profile-789"
}
```

## 🤝 Body Doubling

### Start Body Doubling Session
```http
POST /api/v1/speechbot/dialogue/body-doubling
```

**Description:** Start a virtual body doubling session with AI companion.

**Request Body:**
```json
{
  "user_task": "Complete project documentation",
  "session_duration": 45,
  "encouragement_frequency": "medium",
  "companion_voice_profile": "default",
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "session_type": "deep_work"
}
```

**Response:** Audio stream with opening companion message

**Response Headers:**
```
Content-Type: audio/wav
X-Session-Type: body_doubling
X-Companion-Voice: default
X-Encouragement-Frequency: medium
X-Session-Duration: 45
```

### Body Doubling Session Templates
```http
GET /api/v1/speechbot/body-doubling/templates
```

**Response (200 OK):**
```json
[
  {
    "name": "Pomodoro",
    "duration": 25,
    "description": "Classic 25-minute focus session",
    "encouragement_frequency": "medium",
    "break_reminder": true
  },
  {
    "name": "Deep Work",
    "duration": 90,
    "description": "Extended concentration period",
    "encouragement_frequency": "low",
    "break_reminder": false
  },
  {
    "name": "Quick Task",
    "duration": 15,
    "description": "Short, specific tasks",
    "encouragement_frequency": "high",
    "break_reminder": false
  }
]
```

## 👥 User Management

### Get Current User
```http
GET /api/v1/users/me
```

**Response (200 OK):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "adhd_type": "combined",
  "adhd_severity": "moderate",
  "preferred_modes": ["calm", "focused"],
  "accessibility_needs": ["high_contrast", "large_text"],
  "is_active": true,
  "created_at": "2024-01-10T10:30:00Z",
  "last_login": "2024-01-15T09:15:00Z"
}
```

### Update User Profile
```http
PUT /api/v1/users/me
```

**Request Body:**
```json
{
  "full_name": "John Smith",
  "adhd_type": "inattentive",
  "adhd_severity": "mild",
  "preferred_modes": ["calm", "motivated"],
  "accessibility_needs": ["high_contrast", "reduced_motion"]
}
```

### Get User Preferences
```http
GET /api/v1/speechbot/preferences
```

**Response (200 OK):**
```json
{
  "default_adhd_mode": "calm",
  "enable_nonverbals": true,
  "nonverbal_frequency": 0.15,
  "default_voice_profile": "profile-123",
  "enable_emotion_detection": true,
  "auto_mode_selection": true,
  "accessibility_settings": {
    "high_contrast": true,
    "large_text": false,
    "reduced_motion": true,
    "keyboard_navigation": true
  },
  "notification_settings": {
    "session_reminders": true,
    "achievement_notifications": true,
    "daily_check_ins": false
  }
}
```

### Update User Preferences
```http
PUT /api/v1/speechbot/preferences
```

**Request Body:**
```json
{
  "default_adhd_mode": "focused",
  "enable_emotion_detection": false,
  "accessibility_settings": {
    "high_contrast": true,
    "large_text": true
  }
}
```

## 📊 Analytics and Insights

### Get User Analytics
```http
GET /api/v1/speechbot/analytics
```

**Query Parameters:**
- `days` (optional): Number of days to include (default: 30)
- `include_details` (optional): Include detailed session data (default: false)

**Response (200 OK):**
```json
{
  "summary": {
    "total_sessions": 127,
    "total_audio_duration": 1847.3,
    "average_session_length": 14.5,
    "most_used_mode": "calm",
    "voice_profiles_created": 3,
    "body_doubling_sessions": 23
  },
  "mode_usage": {
    "calm": 45,
    "focused": 32,
    "motivated": 28,
    "excited": 15,
    "overwhelmed": 7
  },
  "daily_activity": [
    {
      "date": "2024-01-15",
      "sessions": 8,
      "duration": 67.2,
      "primary_mode": "focused"
    }
  ],
  "insights": {
    "peak_usage_time": "09:00-11:00",
    "most_productive_mode": "focused",
    "improvement_suggestions": [
      "Consider using 'motivated' mode more often in the morning",
      "Your body doubling sessions show 85% completion rate - excellent!"
    ]
  }
}
```

## 🔧 System Information

### Health Check
```http
GET /api/v1/speechbot/health
```

**Response (200 OK):**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "dia_engine": "ready",
    "emotion_detection": "ready",
    "streaming_engine": "ready",
    "database": "connected",
    "redis": "connected"
  },
  "performance": {
    "average_synthesis_time": 1.8,
    "active_streams": 3,
    "queue_length": 0
  }
}
```

### Get Capabilities
```http
GET /api/v1/speechbot/capabilities
```

**Response (200 OK):**
```json
{
  "engine": "Dia TTS",
  "model_size": "1.6B parameters",
  "supported_languages": ["en"],
  "supported_formats": ["wav", "mp3"],
  "sample_rates": [16000, 22050, 24000, 44100],
  "adhd_modes": ["calm", "excited", "focused", "overwhelmed", "motivated"],
  "features": {
    "voice_cloning": true,
    "emotion_detection": true,
    "real_time_streaming": true,
    "nonverbal_sounds": true,
    "body_doubling": true
  },
  "limits": {
    "max_text_length": 5000,
    "max_audio_duration": 300,
    "max_voice_profiles_per_user": 10,
    "voice_sample_duration": {
      "min": 5,
      "max": 30
    }
  }
}
```

## ❌ Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The provided text is too long for synthesis",
    "details": {
      "field": "text",
      "max_length": 5000,
      "provided_length": 7500
    },
    "suggestions": [
      "Break your text into smaller chunks",
      "Use the streaming synthesis for longer texts"
    ],
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### Common Error Codes

| Code | HTTP Status | Description | ADHD-Friendly Message |
|------|-------------|-------------|----------------------|
| `VALIDATION_ERROR` | 400 | Invalid request data | "Let's fix this together - check the highlighted fields" |
| `AUTHENTICATION_REQUIRED` | 401 | Missing or invalid token | "Please sign in to continue using your voice assistant" |
| `INSUFFICIENT_PERMISSIONS` | 403 | User lacks required permissions | "This feature isn't available for your account yet" |
| `RESOURCE_NOT_FOUND` | 404 | Requested resource doesn't exist | "We couldn't find what you're looking for" |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests | "You're using the assistant a lot! Please wait a moment" |
| `SYNTHESIS_FAILED` | 500 | Voice generation error | "Something went wrong with voice generation - let's try again" |
| `SERVICE_UNAVAILABLE` | 503 | Speechbot service down | "The voice assistant is taking a break - please try again soon" |

---

**🎯 This API reference provides complete documentation for integrating with the world's most advanced ADHD voice assistant platform!**
