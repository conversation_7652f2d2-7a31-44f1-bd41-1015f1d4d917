Project Chronos Documentation
=============================

Welcome to Project Chronos, the comprehensive ADHD-optimized productivity platform designed by and for the neurodivergent community. This documentation provides everything you need to understand, use, and contribute to a system that truly works with ADHD brains.

**🎉 New in v1.0:** Revolutionary **Speechbot** voice assistant powered by Dia TTS (1.6B parameters) with voice cloning, multi-speaker dialogue, and ADHD-optimized emotional support.

.. note::
   Project Chronos is built with ADHD users at the center of every design decision. Whether you're a user looking to boost your productivity or a developer wanting to integrate with our platform, this documentation is designed to be clear, comprehensive, and ADHD-friendly.

.. toctree::
   :maxdepth: 2
   :caption: User Guide:

   user-guide/getting-started

.. toctree::
   :maxdepth: 2
   :caption: Getting Started:

   getting-started
   user-guide/index

.. toctree::
   :maxdepth: 2
   :caption: Features:

   features/index
   features/task-management
   features/time-blocking
   features/focus-sessions
   features/gamification
   features/body-doubling
   features/notifications
   features/integrations

.. toctree::
   :maxdepth: 2
   :caption: Speechbot Voice Assistant:

   speechbot/index
   speechbot/getting-started
   speechbot/voice-cloning
   speechbot/dialogue-generation
   speechbot/adhd-features
   speechbot/api-reference

.. toctree::
   :maxdepth: 2
   :caption: Architecture:

   architecture/system-overview
   architecture/adhd-design-principles

.. toctree::
   :maxdepth: 2
   :caption: API Reference:

   api/api-overview
   api/integrations

.. toctree::
   :maxdepth: 2
   :caption: Development:

   agents/index
   development/index
   development/integration-development
   deployment/index

Quick Start
-----------

Project Chronos is built with a comprehensive, ADHD-first architecture that prioritizes user experience and neurodivergent needs:

**Completed Core Systems (100% Complete)**:
* ✅ **Agent 1**: Core Infrastructure & Database - Complete foundation
* ✅ **Agent 2**: Authentication & Security - JWT auth with refresh tokens
* ✅ **Agent 3**: Task Management & AI - CRUD, AI chunking, filtering
* ✅ **Agent 4**: Time Blocking & Scheduling - Visual time management
* ✅ **Agent 5**: Focus Sessions & Pomodoro - Hyperfocus protection
* ✅ **Agent 6**: Real-time & WebSocket - Body doubling platform
* ✅ **Agent 7**: Notifications & Background Tasks - Context-aware reminders
* ✅ **Agent 8**: Gamification & Motivation - Dopamine-driven rewards
* ✅ **Agent 9**: API Integration & External Services - Google, Todoist, Slack, Notion
* ✅ **Agent 10**: Testing & Quality Assurance - Comprehensive test coverage

**Revolutionary New Feature**:
* 🎭 **Speechbot**: ADHD-optimized voice assistant with Dia TTS (1.6B parameters)
  - Voice cloning from 5-10 second samples
  - Multi-speaker dialogue generation
  - ADHD emotional state optimization
  - Virtual body doubling companions

Key Features
------------

🧠 **ADHD-Optimized Task Management**
   - AI-powered task chunking for overwhelming projects
   - Energy-level matching for optimal task timing
   - Flexible organization with tags and contexts
   - Executive function support with guided workflows

🕐 **Visual Time Management**
   - Circular clock view for intuitive time proportions
   - Timeline interface with drag-and-drop scheduling
   - Automatic buffer time to prevent rushing
   - Conflict detection and resolution suggestions

🎯 **Attention-Aware Focus Sessions**
   - Hyperfocus detection and protection systems
   - Flexible session types (Pomodoro, deep work, sprint)
   - Pause and resume for ADHD attention patterns
   - Break suggestions with ADHD-friendly activities

🤝 **Virtual Body Doubling Platform**
   - Real-time co-working sessions for social accountability
   - Synchronized focus sessions with peers
   - Encouragement system and progress sharing
   - Community support from fellow ADHD users

🔔 **Context-Aware Notifications**
   - Persistent reminders that respect focus sessions
   - Gentle escalation without overwhelming
   - Flexible snoozing and acknowledgment options
   - Batched delivery to reduce interruption frequency

🎮 **Dopamine-Driven Gamification**
   - Achievement system that celebrates effort and progress
   - Points and rewards aligned with ADHD brain chemistry
   - Flexible streak systems that accommodate bad days
   - Motivation analytics and personalized encouragement

🔌 **External Service Integrations**
   - Google Calendar sync with ADHD timing optimizations
   - Todoist task management with energy-aware categorization
   - Slack notifications and body doubling invitations
   - Notion database sync with rich property mapping
   - OAuth 2.0 security with gentle error handling

🧪 **Comprehensive Testing & Quality Assurance**
   - 90%+ test coverage with ADHD-specific test scenarios
   - Unit, integration, performance, and accessibility testing
   - End-to-end user workflow validation
   - Automated CI/CD pipeline with quality gates
   - Deployment readiness assessment tools

🎭 **Revolutionary Voice Assistant (Speechbot)**
   - Dia TTS engine with 1.6B parameters for superior voice quality
   - Voice cloning from 5-10 second audio samples
   - Multi-speaker dialogue generation (up to 4 speakers)
   - ADHD emotional state optimization (calm, excited, focused, overwhelmed, motivated)
   - Nonverbal communication (laughs, sighs, gasps, whispers)
   - Virtual body doubling companions for accountability
   - Therapeutic dialogue scenarios for social practice
   - Real-time speech synthesis (2x realtime on RTX 4090)

Getting Started
---------------

1. **Installation**::

    git clone https://github.com/forkrul/day1-idea.git
    cd day1-idea
    pip install -r requirements.txt

2. **Configuration**::

    cp .env.example .env
    # Edit .env with your settings

3. **Database Setup**::

    alembic upgrade head

4. **Run the Application**::

    uvicorn app.main:app --reload

Architecture Overview
--------------------

Project Chronos uses a modern, scalable architecture:

- **Backend**: FastAPI with async/await support
- **Database**: PostgreSQL with SQLAlchemy 2.0+
- **Real-time**: WebSockets with Redis for state management
- **Authentication**: JWT with secure session management
- **AI Integration**: OpenAI and Anthropic for intelligent features

Current Implementation Status
----------------------------

**Project Completion: 100% (10 of 10 agents complete)**

.. list-table:: Agent Implementation Status
   :header-rows: 1
   :widths: 20 30 20 30

   * - Agent
     - Functionality
     - Status
     - Key Features
   * - Agent 1
     - Core Infrastructure
     - ✅ **Complete**
     - Database, Models, Config, Base Setup
   * - Agent 2
     - Authentication & Security
     - ✅ **Complete**
     - JWT Auth, Password Management, Security
   * - Agent 3
     - Task Management & AI
     - ✅ **Complete**
     - CRUD, AI Chunking, Filtering, Organization
   * - Agent 4
     - Time Blocking & Scheduling
     - ✅ **Complete**
     - Visual Time Interface, Buffer Management
   * - Agent 5
     - Focus Sessions & Pomodoro
     - ✅ **Complete**
     - Hyperfocus Protection, Flexible Sessions
   * - Agent 6
     - Real-time & WebSocket
     - ✅ **Complete**
     - Body Doubling, Live Collaboration
   * - Agent 7
     - Notifications & Background Tasks
     - ✅ **Complete**
     - Context-Aware Reminders, Celery Workers
   * - Agent 8
     - Gamification & Motivation
     - ✅ **Complete**
     - Points, Achievements, Dopamine Menu
   * - Agent 9
     - API Integration & External Services
     - ✅ **Complete**
     - Google Calendar, Todoist, Slack, Notion
   * - Agent 10
     - Testing & Quality Assurance
     - ✅ **Complete**
     - Unit/Integration/E2E Tests, CI/CD Pipeline

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
