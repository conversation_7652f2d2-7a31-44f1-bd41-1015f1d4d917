"""
Main FastAPI application for Project Chronos.

This module creates and configures the FastAPI application with
ADHD-focused features and comprehensive middleware.
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi
import time
import logging

from chronos.app.core.config import settings
from chronos.app.core.exceptions import ChronosException

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)

logger = logging.getLogger(__name__)


def create_application() -> FastAPI:
    """
    Create and configure FastAPI application.
    
    Returns:
        FastAPI: Configured application instance
    """
    
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description="🧠 ADHD-focused productivity and time management API with Speechbot integration",
        version=settings.VERSION,
        docs_url=None,  # We'll create custom dark mode docs
        redoc_url=None,  # We'll create custom dark mode redoc
        openapi_url="/openapi.json",
        # ADHD-friendly API documentation
        openapi_tags=[
            {
                "name": "Authentication",
                "description": "User authentication and authorization"
            },
            {
                "name": "Users",
                "description": "User management with ADHD-specific preferences"
            },
            {
                "name": "Tasks",
                "description": "Task management with AI chunking and energy levels"
            },
            {
                "name": "Time Blocking",
                "description": "Visual time management and scheduling"
            },
            {
                "name": "Focus Sessions",
                "description": "Pomodoro timers and hyperfocus tracking"
            },
            {
                "name": "Notifications",
                "description": "Persistent reminders and gentle notifications"
            },
            {
                "name": "Gamification",
                "description": "Points, achievements, and motivation features"
            },
            {
                "name": "System",
                "description": "Health checks and system information"
            }
        ]
    )
    
    # Add enhanced CORS middleware for API documentation and frontend
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "http://localhost:3000",  # Next.js dev server
            "http://localhost:8090",  # Traefik proxy
            "http://chronos.autism.localhost:8090",  # Main UI
            "http://api.autism.localhost:8090",  # API docs
            "http://speechbot.autism.localhost:8090",  # Speechbot UI
            "http://127.0.0.1:3000",
            "http://127.0.0.1:8090",
            *settings.BACKEND_CORS_ORIGINS,  # Additional configured origins
        ],
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=[
            "Accept",
            "Accept-Language",
            "Content-Language",
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "X-API-Key",
            "Cache-Control",
            "Pragma",
        ],
        expose_headers=["X-Total-Count", "X-Page-Count", "X-Current-Page"],
    )
    
    # Add trusted host middleware for security
    if not settings.TESTING:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", settings.SERVER_NAME]
        )
    
    # Global exception handlers
    @app.exception_handler(ChronosException)
    async def chronos_exception_handler(request: Request, exc: ChronosException):
        """Handle custom Chronos exceptions with ADHD-friendly messages."""
        logger.error(f"Chronos exception: {exc.message}", exc_info=exc)
        
        return JSONResponse(
            status_code=400,
            content={
                "error": exc.to_dict(),
                "timestamp": time.time(),
                "path": str(request.url.path)
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions gracefully."""
        logger.error(f"Unexpected error: {exc}", exc_info=True)
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "An unexpected error occurred",
                    "friendly_message": "Something went wrong on our end. We're working to fix it!",
                },
                "timestamp": time.time(),
                "path": str(request.url.path)
            }
        )
    
    # Health check endpoint
    @app.get("/health", tags=["System"])
    async def health_check():
        """
        Health check endpoint for monitoring.
        
        Returns basic application health status and ADHD-specific metrics.
        """
        from chronos.app.core.database import db_manager
        
        # Check database health
        db_health = await db_manager.health_check()
        
        return {
            "status": "healthy" if db_health["status"] == "healthy" else "unhealthy",
            "timestamp": time.time(),
            "version": settings.VERSION,
            "environment": "development" if settings.TESTING else "production",
            "database": db_health,
            "adhd_features": {
                "ai_chunking_enabled": settings.AI_CHUNKING_ENABLED,
                "default_chunk_size": settings.DEFAULT_CHUNK_SIZE,
                "hyperfocus_threshold": settings.HYPERFOCUS_WARNING_THRESHOLD,
            }
        }
    
    # Custom dark mode Swagger UI
    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        """Custom dark mode Swagger UI for ADHD-friendly documentation."""
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title=f"{app.title} - Interactive API Documentation",
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
            swagger_ui_parameters={
                "theme": "dark",
                "syntaxHighlight.theme": "tomorrow-night",
                "tryItOutEnabled": True,
                "displayRequestDuration": True,
                "filter": True,
                "showExtensions": True,
                "showCommonExtensions": True,
                "deepLinking": True,
                "displayOperationId": False,
                "defaultModelsExpandDepth": 2,
                "defaultModelExpandDepth": 2,
                "docExpansion": "list",
                "persistAuthorization": True,
                "layout": "BaseLayout",
            },
            swagger_ui_init_oauth={
                "usePkceWithAuthorizationCodeGrant": True,
            }
        )

    # Custom dark mode ReDoc
    @app.get("/redoc", include_in_schema=False)
    async def redoc_html():
        """Custom dark mode ReDoc for comprehensive API documentation."""
        return get_redoc_html(
            openapi_url=app.openapi_url,
            title=f"{app.title} - API Reference",
            redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@2.1.3/bundles/redoc.standalone.js",
            redoc_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
            with_google_fonts=True,
        )

    # Enhanced OpenAPI schema
    def custom_openapi():
        """Generate custom OpenAPI schema with ADHD-specific enhancements."""
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description="""
# 🧠 Project Chronos + Speechbot API

**The World's Most Advanced ADHD-Optimized Voice Assistant Platform**

## 🌟 Features

- **🎭 Advanced Voice Synthesis** with 5 ADHD emotional modes
- **🧠 Real-Time Emotion Detection** for automatic mode selection
- **🤝 Virtual Body Doubling** companions for work sessions
- **🎤 Personal Voice Cloning** for self-compassion exercises
- **📊 ADHD Analytics** with usage insights and recommendations
- **⚡ Real-Time Streaming** for immediate audio feedback

## 🎯 ADHD-Optimized Design

This API is specifically designed for ADHD users with:
- Clear, consistent response formats
- Helpful error messages with suggestions
- Reduced cognitive load in interface design
- Evidence-based features for executive function support

## 🔐 Authentication

Most endpoints require authentication using JWT tokens:

```bash
Authorization: Bearer <your_jwt_token>
```

Get your token from the `/auth/login` endpoint.

## 🚀 Getting Started

1. **Register**: Create an account with `/auth/register`
2. **Login**: Get your JWT token with `/auth/login`
3. **Explore**: Try the voice synthesis endpoints
4. **Customize**: Set up your ADHD preferences

## 📞 Support

- **Documentation**: Complete guides available in `/docs`
- **Community**: ADHD-focused support community
- **Research**: Evidence-based feature development
            """,
            routes=app.routes,
        )

        # Add ADHD-specific extensions
        openapi_schema["info"]["x-logo"] = {
            "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
        }

        # Add server information
        openapi_schema["servers"] = [
            {
                "url": "http://api.autism.localhost:8090",
                "description": "Development server (Traefik proxy)"
            },
            {
                "url": "http://localhost:8000",
                "description": "Direct development server"
            }
        ]

        # Add security schemes
        openapi_schema["components"]["securitySchemes"] = {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "JWT token obtained from /auth/login"
            },
            "ApiKeyAuth": {
                "type": "apiKey",
                "in": "header",
                "name": "X-API-Key",
                "description": "API key for service integrations"
            }
        }

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # Root endpoint with ADHD-friendly welcome
    @app.get("/", tags=["System"])
    async def root():
        """
        Root endpoint with welcome message.

        Provides a friendly introduction to the ADHD-focused API.
        """
        return {
            "message": "Welcome to Project Chronos + Speechbot! 🧠🎭",
            "description": "ADHD-focused productivity platform with advanced voice assistant",
            "version": settings.VERSION,
            "documentation": {
                "interactive": "/docs",
                "reference": "/redoc",
                "openapi": "/openapi.json"
            },
            "features": [
                "🎤 ADHD-optimized voice synthesis",
                "🧠 Real-time emotion detection",
                "🤝 Virtual body doubling companions",
                "🎯 AI-powered task chunking",
                "📊 Visual time management",
                "⚡ Real-time audio streaming",
                "🎮 Gamified motivation system"
            ],
            "adhd_optimized": True,
            "accessibility": "WCAG 2.1 AA compliant"
        }
    
    # Include API routers (will be added as other agents are implemented)
    # app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
    # app.include_router(users_router, prefix="/api/v1/users", tags=["Users"])
    # app.include_router(tasks_router, prefix="/api/v1/tasks", tags=["Tasks"])
    
    return app


# Create application instance
app = create_application()


# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Application startup tasks."""
    logger.info("🧠 Starting Project Chronos...")
    logger.info(f"📊 Environment: {'Testing' if settings.TESTING else 'Development'}")
    logger.info(f"🗄️  Database: {str(settings.DATABASE_URL).split('@')[-1]}")
    logger.info(f"🤖 AI Chunking: {'Enabled' if settings.AI_CHUNKING_ENABLED else 'Disabled'}")
    
    # Check database connection
    from chronos.app.core.database import check_db_connection
    if await check_db_connection():
        logger.info("✅ Database connection established")
    else:
        logger.error("❌ Database connection failed")


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown tasks."""
    logger.info("🛑 Shutting down Project Chronos...")
    
    # Close database connections
    from chronos.app.core.database import engine
    await engine.dispose()
    logger.info("✅ Database connections closed")


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "chronos.app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.LOG_LEVEL.lower()
    )
