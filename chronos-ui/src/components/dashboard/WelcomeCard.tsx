'use client'

import { useEffect, useState } from 'react'
import { getTimeOfDayGreeting } from '@/lib/utils'
import { FireIcon, CpuChipIcon, ClockIcon } from '@heroicons/react/24/outline'

interface WelcomeCardProps {
  user: any
  adhdState: any
}

export function WelcomeCard({ user, adhdState }: WelcomeCardProps) {
  const [greeting, setGreeting] = useState('')
  const [motivationalMessage, setMotivationalMessage] = useState('')

  useEffect(() => {
    setGreeting(getTimeOfDayGreeting())
    setMotivationalMessage(generateMotivationalMessage(adhdState))
  }, [adhdState])

  const generateMotivationalMessage = (state: any): string => {
    const messages = {
      high_energy: [
        "You're energized and ready to tackle anything! 🚀",
        "Great energy today! Perfect time for challenging tasks.",
        "You're on fire! Channel that energy into your priorities.",
      ],
      medium_energy: [
        "Steady energy - a great foundation for productive work.",
        "You're in a good flow state. Keep the momentum going!",
        "Balanced energy is perfect for sustained focus.",
      ],
      low_energy: [
        "Low energy days are perfect for gentle, nurturing tasks.",
        "Be kind to yourself today. Small steps count too.",
        "Rest is productive too. Honor what your body needs.",
      ],
    }

    const energyCategory = 
      state.energyLevel > 7 ? 'high_energy' :
      state.energyLevel > 4 ? 'medium_energy' : 'low_energy'

    const categoryMessages = messages[energyCategory]
    return categoryMessages[Math.floor(Math.random() * categoryMessages.length)]
  }

  const getFocusModeEmoji = (mode: string): string => {
    const emojis = {
      normal: '🎯',
      deep_work: '🧠',
      creative: '🎨',
      administrative: '📋',
      break: '🌱',
      overwhelmed: '🤗',
    }
    return emojis[mode as keyof typeof emojis] || '🎯'
  }

  const getEnergyColor = (level: number): string => {
    if (level <= 3) return 'text-red-500 bg-red-100'
    if (level <= 6) return 'text-yellow-500 bg-yellow-100'
    return 'text-green-500 bg-green-100'
  }

  const getCognitiveLoadColor = (load: number): string => {
    if (load >= 0.7) return 'text-red-500 bg-red-100'
    if (load >= 0.4) return 'text-yellow-500 bg-yellow-100'
    return 'text-green-500 bg-green-100'
  }

  return (
    <div className="card">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {greeting}, {user.name}! 👋
          </h1>
          <p className="text-gray-600 mb-4">
            {motivationalMessage}
          </p>

          {/* ADHD State Overview */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {/* Energy Level */}
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${getEnergyColor(adhdState.energyLevel)}`}>
                <FireIcon className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Energy Level</p>
                <p className="text-lg font-bold text-gray-700">
                  {adhdState.energyLevel}/10
                </p>
              </div>
            </div>

            {/* Cognitive Load */}
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${getCognitiveLoadColor(adhdState.cognitiveLoad)}`}>
                <CpuChipIcon className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Cognitive Load</p>
                <p className="text-lg font-bold text-gray-700">
                  {Math.round(adhdState.cognitiveLoad * 100)}%
                </p>
              </div>
            </div>

            {/* Focus Mode */}
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-primary-100 text-primary-500">
                <span className="text-lg">{getFocusModeEmoji(adhdState.focusMode)}</span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Focus Mode</p>
                <p className="text-sm font-semibold text-gray-700 capitalize">
                  {adhdState.focusMode.replace('_', ' ')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="ml-6 flex flex-col space-y-2">
          <button className="btn-primary text-sm px-3 py-2">
            Start Focus Session
          </button>
          <button className="btn-secondary text-sm px-3 py-2">
            Quick Task
          </button>
        </div>
      </div>

      {/* Today's Focus */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          Today's Focus
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-primary-50 rounded-lg p-4">
            <h4 className="font-medium text-primary-900 mb-2">
              🎯 Primary Goal
            </h4>
            <p className="text-sm text-primary-700">
              Complete the quarterly review presentation
            </p>
          </div>
          <div className="bg-secondary-50 rounded-lg p-4">
            <h4 className="font-medium text-secondary-900 mb-2">
              🌟 Bonus Goal
            </h4>
            <p className="text-sm text-secondary-700">
              Organize digital workspace
            </p>
          </div>
        </div>
      </div>

      {/* ADHD Tips */}
      {adhdState.cognitiveLoad > 0.6 && (
        <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-start">
            <span className="text-yellow-600 mr-2">💡</span>
            <div>
              <h4 className="text-sm font-medium text-yellow-800 mb-1">
                ADHD Tip for High Cognitive Load
              </h4>
              <p className="text-sm text-yellow-700">
                Break large tasks into smaller, 15-minute chunks. Your brain will thank you!
              </p>
            </div>
          </div>
        </div>
      )}

      {adhdState.energyLevel <= 3 && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start">
            <span className="text-blue-600 mr-2">⚡</span>
            <div>
              <h4 className="text-sm font-medium text-blue-800 mb-1">
                Energy Boost Suggestions
              </h4>
              <p className="text-sm text-blue-700">
                Try a 5-minute walk, drink some water, or do light stretching to naturally boost your energy.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
