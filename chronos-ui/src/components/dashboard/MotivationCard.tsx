'use client'

import { TrophyIcon, StarIcon, FireIcon } from '@heroicons/react/24/outline'

export function MotivationCard() {
  const achievements = [
    { id: 1, title: 'Task Crusher', description: 'Completed 5 tasks today', icon: '🎯', earned: true },
    { id: 2, title: 'Focus Master', description: 'Completed 3 focus sessions', icon: '🧠', earned: true },
    { id: 3, title: 'Streak Keeper', description: '7-day completion streak', icon: '🔥', earned: false },
  ]

  const currentLevel = 12
  const currentXP = 850
  const nextLevelXP = 1000

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-yellow-100 rounded-lg mr-3">
            <TrophyIcon className="w-5 h-5 text-yellow-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Motivation</h3>
            <p className="text-sm text-gray-600">Level {currentLevel} • {currentXP}/{nextLevelXP} XP</p>
          </div>
        </div>
        <div className="text-2xl">🏆</div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500"
            style={{ width: `${(currentXP / nextLevelXP) * 100}%` }}
          />
        </div>
        <p className="text-xs text-gray-500 mt-1">
          {nextLevelXP - currentXP} XP to level {currentLevel + 1}
        </p>
      </div>

      {/* Recent Achievements */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900">Recent Achievements</h4>
        {achievements.map((achievement) => (
          <div
            key={achievement.id}
            className={`flex items-center p-3 rounded-lg ${
              achievement.earned 
                ? 'bg-yellow-50 border border-yellow-200' 
                : 'bg-gray-50 border border-gray-200 opacity-60'
            }`}
          >
            <span className="text-2xl mr-3">{achievement.icon}</span>
            <div className="flex-1">
              <h5 className="text-sm font-medium text-gray-900">
                {achievement.title}
              </h5>
              <p className="text-xs text-gray-600">
                {achievement.description}
              </p>
            </div>
            {achievement.earned && (
              <StarIcon className="w-5 h-5 text-yellow-500" />
            )}
          </div>
        ))}
      </div>

      {/* Daily Challenge */}
      <div className="mt-4 p-3 bg-primary-50 rounded-lg border border-primary-200">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-primary-900">
              🎯 Daily Challenge
            </h4>
            <p className="text-sm text-primary-700">
              Complete 3 focus sessions
            </p>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-primary-600">2/3</div>
            <div className="text-xs text-primary-500">+50 XP</div>
          </div>
        </div>
      </div>
    </div>
  )
}
