import * as React from "react"
import { cn } from "@/lib/utils"
import { Badge } from "./badge"
import { Brain, Zap, Star, Sparkles, Crown } from "lucide-react"

type ComplexityLevel = "trivial" | "simple" | "moderate" | "complex" | "expert"

interface ComplexityIndicatorProps {
  level: ComplexityLevel
  className?: string
  showIcon?: boolean
  showLabel?: boolean
  variant?: "default" | "detailed" | "minimal"
  size?: "sm" | "md" | "lg"
}

const ComplexityIndicator = React.forwardRef<
  HTMLDivElement,
  ComplexityIndicatorProps
>(({ 
  level, 
  className, 
  showIcon = true, 
  showLabel = true,
  variant = "default",
  size = "md",
  ...props 
}, ref) => {
  const complexityConfig = {
    trivial: {
      label: "Trivial",
      description: "Quick and easy task",
      color: "bg-gray-100 text-gray-800 border-gray-200",
      icon: <div className="w-2 h-2 bg-gray-400 rounded-full" />,
      dots: 1
    },
    simple: {
      label: "Simple", 
      description: "Straightforward with minimal thinking",
      color: "bg-green-100 text-green-800 border-green-200",
      icon: <Zap className="h-3 w-3" />,
      dots: 2
    },
    moderate: {
      label: "Moderate",
      description: "Requires some focus and planning", 
      color: "bg-blue-100 text-blue-800 border-blue-200",
      icon: <Brain className="h-3 w-3" />,
      dots: 3
    },
    complex: {
      label: "Complex",
      description: "Challenging task requiring deep focus",
      color: "bg-orange-100 text-orange-800 border-orange-200", 
      icon: <Star className="h-3 w-3" />,
      dots: 4
    },
    expert: {
      label: "Expert",
      description: "Highly complex, requires peak performance",
      color: "bg-purple-100 text-purple-800 border-purple-200",
      icon: <Crown className="h-3 w-3" />,
      dots: 5
    }
  }

  const config = complexityConfig[level]
  
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-3 py-1", 
    lg: "text-base px-4 py-2"
  }

  if (variant === "minimal") {
    return (
      <div
        ref={ref}
        className={cn("flex items-center space-x-1", className)}
        {...props}
      >
        {showIcon && config.icon}
        {showLabel && (
          <span className={cn("font-medium", sizeClasses[size])}>
            {config.label}
          </span>
        )}
      </div>
    )
  }

  if (variant === "detailed") {
    return (
      <div
        ref={ref}
        className={cn("space-y-2", className)}
        {...props}
      >
        <div className="flex items-center space-x-2">
          {showIcon && (
            <div className={cn("p-1 rounded", config.color)}>
              {config.icon}
            </div>
          )}
          {showLabel && (
            <span className="font-medium">{config.label}</span>
          )}
          
          {/* Complexity dots */}
          <div className="flex space-x-1">
            {Array.from({ length: 5 }, (_, i) => (
              <div
                key={i}
                className={cn(
                  "w-2 h-2 rounded-full",
                  i < config.dots 
                    ? "bg-current opacity-100" 
                    : "bg-current opacity-20"
                )}
              />
            ))}
          </div>
        </div>
        
        <p className="text-xs text-muted-foreground">
          {config.description}
        </p>
      </div>
    )
  }

  // Default variant
  return (
    <Badge
      ref={ref}
      className={cn(
        "flex items-center space-x-1 border",
        config.color,
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {showIcon && config.icon}
      {showLabel && <span>{config.label}</span>}
    </Badge>
  )
})

ComplexityIndicator.displayName = "ComplexityIndicator"

export { ComplexityIndicator, type ComplexityLevel }
