'use client'

import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { useADHDState } from '@/hooks/useADHDState'
import {
  HomeIcon,
  CheckIcon,
  ClockIcon,
  ChartBarIcon,
  CogIcon,
  UserIcon,
  XMarkIcon,
  FireIcon,
  CpuChipIcon,
} from '@heroicons/react/24/outline'
import { cn } from '@/lib/utils'

interface SidebarProps {
  open: boolean
  onClose: () => void
}

const navigation = [
  { name: 'Dashboard', href: '/', icon: HomeIcon },
  { name: 'Tasks', href: '/tasks', icon: CheckIcon },
  { name: 'Focus Sessions', href: '/focus', icon: ClockIcon },
  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon },
  { name: 'ADHD Insights', href: '/adhd', icon: CpuChipIcon },
  { name: 'Settings', href: '/settings', icon: CogIcon },
]

export function Sidebar({ open, onClose }: SidebarProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const { adhdState } = useADHDState()

  const handleNavigation = (href: string) => {
    router.push(href)
    onClose()
  }

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      {/* Logo and brand */}
      <div className="flex h-16 shrink-0 items-center px-6 border-b border-gray-200">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
            <ClockIcon className="w-5 h-5 text-white" />
          </div>
          <span className="ml-3 text-xl font-bold text-gray-900">Chronos</span>
        </div>
      </div>

      {/* User info with ADHD state */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            <UserIcon className="w-6 h-6 text-primary-600" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-900">{user?.name}</p>
            <p className="text-xs text-gray-500">{user?.email}</p>
          </div>
        </div>
        
        {/* ADHD State Indicators */}
        <div className="mt-3 flex items-center space-x-4">
          <div className="flex items-center">
            <FireIcon className="w-4 h-4 text-orange-500 mr-1" />
            <span className="text-xs text-gray-600">
              Energy: {adhdState.energyLevel}/10
            </span>
          </div>
          <div className="flex items-center">
            <CpuChipIcon className="w-4 h-4 text-blue-500 mr-1" />
            <span className="text-xs text-gray-600">
              Load: {Math.round(adhdState.cognitiveLoad * 100)}%
            </span>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-6 py-4">
        <ul className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <li key={item.name}>
                <button
                  onClick={() => handleNavigation(item.href)}
                  className={cn(
                    'nav-item w-full text-left',
                    isActive ? 'nav-item-active' : 'nav-item-inactive'
                  )}
                >
                  <item.icon className="w-5 h-5 mr-3" />
                  {item.name}
                </button>
              </li>
            )
          })}
        </ul>
      </nav>

      {/* Focus mode indicator */}
      <div className="px-6 py-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">Focus Mode</span>
          <span className={cn(
            'px-2 py-1 text-xs rounded-full',
            adhdState.focusMode === 'deep_work' && 'bg-blue-100 text-blue-800',
            adhdState.focusMode === 'creative' && 'bg-purple-100 text-purple-800',
            adhdState.focusMode === 'administrative' && 'bg-green-100 text-green-800',
            adhdState.focusMode === 'break' && 'bg-yellow-100 text-yellow-800',
            adhdState.focusMode === 'normal' && 'bg-gray-100 text-gray-800'
          )}>
            {adhdState.focusMode.replace('_', ' ')}
          </span>
        </div>
      </div>

      {/* Logout */}
      <div className="px-6 py-4 border-t border-gray-200">
        <button
          onClick={logout}
          className="w-full text-left text-sm text-gray-600 hover:text-gray-900"
        >
          Sign out
        </button>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                  <button
                    type="button"
                    className="-m-2.5 p-2.5"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6 text-white" />
                  </button>
                </div>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white">
                  <SidebarContent />
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white border-r border-gray-200">
          <SidebarContent />
        </div>
      </div>
    </>
  )
}
