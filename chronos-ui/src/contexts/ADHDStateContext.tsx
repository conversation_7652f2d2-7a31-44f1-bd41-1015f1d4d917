'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { api } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'

interface ADHDState {
  energyLevel: number // 1-10
  cognitiveLoad: number // 0-1
  focusMode: 'normal' | 'deep_work' | 'creative' | 'administrative' | 'break' | 'overwhelmed'
  emotionalState: 'neutral' | 'excited' | 'calm' | 'anxious' | 'frustrated' | 'motivated'
  stressLevel: number // 0-1
  attentionSpan: number // minutes
  lastUpdated: string
}

interface ADHDStateContextType {
  adhdState: ADHDState
  updateEnergyLevel: (level: number) => Promise<void>
  updateCognitiveLoad: (load: number) => Promise<void>
  updateFocusMode: (mode: ADHDState['focusMode']) => Promise<void>
  updateEmotionalState: (state: ADHDState['emotionalState']) => Promise<void>
  updateFullState: (state: Partial<ADHDState>) => Promise<void>
  isLoading: boolean
}

const defaultADHDState: ADHDState = {
  energyLevel: 5,
  cognitiveLoad: 0.3,
  focusMode: 'normal',
  emotionalState: 'neutral',
  stressLevel: 0.3,
  attentionSpan: 25,
  lastUpdated: new Date().toISOString(),
}

export const ADHDStateContext = createContext<ADHDStateContextType | undefined>(undefined)

export function ADHDStateProvider({ children }: { children: ReactNode }) {
  const [adhdState, setADHDState] = useState<ADHDState>(defaultADHDState)
  const [isLoading, setIsLoading] = useState(false)
  const { user } = useAuth()

  useEffect(() => {
    if (user) {
      loadADHDState()
    }
  }, [user])

  const loadADHDState = async () => {
    try {
      setIsLoading(true)
      const response = await api.get('/adhd/state')
      setADHDState(response.data)
    } catch (error) {
      console.error('Failed to load ADHD state:', error)
      // Use default state if loading fails
      setADHDState(defaultADHDState)
    } finally {
      setIsLoading(false)
    }
  }

  const updateADHDState = async (updates: Partial<ADHDState>) => {
    try {
      const newState = {
        ...adhdState,
        ...updates,
        lastUpdated: new Date().toISOString(),
      }
      
      setADHDState(newState)
      
      // Update on server
      await api.patch('/adhd/state', updates)
    } catch (error) {
      console.error('Failed to update ADHD state:', error)
      // Revert on error
      setADHDState(adhdState)
      throw error
    }
  }

  const updateEnergyLevel = async (level: number) => {
    await updateADHDState({ energyLevel: level })
  }

  const updateCognitiveLoad = async (load: number) => {
    await updateADHDState({ cognitiveLoad: load })
  }

  const updateFocusMode = async (mode: ADHDState['focusMode']) => {
    await updateADHDState({ focusMode: mode })
  }

  const updateEmotionalState = async (state: ADHDState['emotionalState']) => {
    await updateADHDState({ emotionalState: state })
  }

  const updateFullState = async (state: Partial<ADHDState>) => {
    await updateADHDState(state)
  }

  const value = {
    adhdState,
    updateEnergyLevel,
    updateCognitiveLoad,
    updateFocusMode,
    updateEmotionalState,
    updateFullState,
    isLoading,
  }

  return <ADHDStateContext.Provider value={value}>{children}</ADHDStateContext.Provider>
}


