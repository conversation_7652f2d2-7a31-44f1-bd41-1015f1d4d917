@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }
  
  body {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }
}

@layer components {
  /* ADHD-optimized button styles */
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }
  
  .btn-success {
    @apply bg-success-500 hover:bg-success-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2;
  }
  
  .btn-warning {
    @apply bg-warning-500 hover:bg-warning-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-warning-500 focus:ring-offset-2;
  }
  
  .btn-error {
    @apply bg-error-500 hover:bg-error-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-error-500 focus:ring-offset-2;
  }
  
  /* ADHD-optimized card styles */
  .card {
    @apply bg-white rounded-xl shadow-gentle border border-gray-200 p-6 transition-shadow duration-200 hover:shadow-lg;
  }
  
  .card-focus {
    @apply ring-2 ring-focus ring-offset-2;
  }
  
  /* ADHD-optimized form styles */
  .form-input {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-error {
    @apply text-error-600 text-sm mt-1;
  }
  
  /* ADHD-optimized navigation */
  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }
  
  .nav-item-active {
    @apply bg-primary-100 text-primary-700;
  }
  
  .nav-item-inactive {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
  
  /* ADHD-optimized focus states */
  .focus-visible {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  /* Energy level indicators */
  .energy-low {
    @apply bg-energy-low border-red-300;
  }
  
  .energy-medium {
    @apply bg-energy-medium border-yellow-300;
  }
  
  .energy-high {
    @apply bg-energy-high border-green-300;
  }
  
  /* Cognitive load indicators */
  .cognitive-low {
    @apply bg-cognitive-low border-green-300;
  }
  
  .cognitive-medium {
    @apply bg-cognitive-medium border-yellow-300;
  }
  
  .cognitive-high {
    @apply bg-cognitive-high border-red-300;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-bounce-gentle {
    animation: bounceGentle 2s infinite;
  }
  
  /* Accessibility improvements */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .card {
      @apply border-2 border-gray-900;
    }
    
    .btn-primary {
      @apply border-2 border-primary-700;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .card {
      @apply bg-gray-800 border-gray-700 text-white;
    }
    
    .form-input {
      @apply bg-gray-700 border-gray-600 text-white;
    }
  }
}
