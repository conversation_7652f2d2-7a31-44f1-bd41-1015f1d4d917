/**
 * Cognitive Load Dashboard Component
 * 
 * ADHD-optimized interface for tracking mental energy and cognitive load.
 * Features visual indicators, quick assessments, and actionable recommendations.
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { EnergyMeter } from '@/components/ui/energy-meter';
import { ComplexityIndicator } from '@/components/ui/complexity-indicator';
import { 
  Brain, 
  Battery, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  TrendingUp,
  Lightbulb,
  Coffee
} from 'lucide-react';

interface CognitiveState {
  energy_level: number;
  focus_level: number;
  available_capacity: number;
  capacity_percentage: number;
  immediate_action: string;
  action_type: string;
  recommended_tasks: string[];
  should_take_break: boolean;
  optimal_for_complex_work: boolean;
}

interface TaskRecommendation {
  current_energy: number;
  available_capacity: number;
  recommended_tasks: string[];
  capacity_percentage: number;
  suggestions: string[];
}

const CognitiveLoadDashboard: React.FC = () => {
  const [energyLevel, setEnergyLevel] = useState<number>(5);
  const [focusLevel, setFocusLevel] = useState<number>(5);
  const [cognitiveState, setCognitiveState] = useState<CognitiveState | null>(null);
  const [recommendations, setRecommendations] = useState<TaskRecommendation | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Quick cognitive check
  const performQuickCheck = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would call the API
      // For demo purposes, we'll simulate the response
      const mockResponse: CognitiveState = {
        energy_level: energyLevel,
        focus_level: focusLevel,
        available_capacity: Math.max(0, Math.min(10, energyLevel - (10 - focusLevel))),
        capacity_percentage: Math.max(0, Math.min(100, (energyLevel - (10 - focusLevel)) * 10)),
        immediate_action: getImmediateAction(energyLevel, focusLevel),
        action_type: getActionType(energyLevel, focusLevel),
        recommended_tasks: getRecommendedTasks(energyLevel, focusLevel),
        should_take_break: (energyLevel - (10 - focusLevel)) <= 3,
        optimal_for_complex_work: (energyLevel - (10 - focusLevel)) >= 7
      };
      
      setCognitiveState(mockResponse);
    } catch (error) {
      console.error('Error performing cognitive check:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions for demo
  const getImmediateAction = (energy: number, focus: number): string => {
    const capacity = energy - (10 - focus);
    if (capacity <= 3) return "Take a break - your cognitive capacity is low";
    if (capacity <= 6) return "Good time for routine tasks and light work";
    return "Excellent time for challenging, focused work";
  };

  const getActionType = (energy: number, focus: number): string => {
    const capacity = energy - (10 - focus);
    if (capacity <= 3) return "rest";
    if (capacity <= 6) return "light_work";
    return "deep_work";
  };

  const getRecommendedTasks = (energy: number, focus: number): string[] => {
    const capacity = energy - (10 - focus);
    if (capacity <= 3) return ["rest", "light_stretching", "mindful_breathing", "gentle_movement"];
    if (capacity <= 6) return ["routine_tasks", "email_processing", "documentation", "light_coding"];
    return ["complex_analysis", "creative_work", "learning_new_skills", "strategic_planning"];
  };

  const getCapacityColor = (percentage: number): string => {
    if (percentage <= 30) return "bg-red-500";
    if (percentage <= 60) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getEnergyIcon = (level: number) => {
    if (level <= 3) return <Battery className="h-5 w-5 text-red-500" />;
    if (level <= 6) return <Battery className="h-5 w-5 text-yellow-500" />;
    return <Battery className="h-5 w-5 text-green-500" />;
  };

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'rest': return <Coffee className="h-5 w-5 text-blue-500" />;
      case 'light_work': return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'deep_work': return <Brain className="h-5 w-5 text-green-500" />;
      default: return <Lightbulb className="h-5 w-5" />;
    }
  };

  // Auto-update when sliders change
  useEffect(() => {
    const timer = setTimeout(() => {
      if (energyLevel > 0 && focusLevel > 0) {
        performQuickCheck();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [energyLevel, focusLevel]);

  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Cognitive Load Dashboard
        </h1>
        <p className="text-gray-600">
          Track your mental energy and get personalized task recommendations
        </p>
      </div>

      {/* Input Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Current State Assessment
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="block text-sm font-medium mb-2">
              Energy Level: {energyLevel}/10
            </label>
            <Slider
              value={[energyLevel]}
              onValueChange={(value) => setEnergyLevel(value[0])}
              max={10}
              min={1}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Depleted</span>
              <span>Peak Energy</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Focus Level: {focusLevel}/10
            </label>
            <Slider
              value={[focusLevel]}
              onValueChange={(value) => setFocusLevel(value[0])}
              max={10}
              min={1}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Scattered</span>
              <span>Laser Focused</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cognitive State Results */}
      {cognitiveState && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Capacity Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getEnergyIcon(cognitiveState.energy_level)}
                Cognitive Capacity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Available Capacity</span>
                    <span className="text-lg font-bold">
                      {cognitiveState.capacity_percentage.toFixed(0)}%
                    </span>
                  </div>
                  <Progress 
                    value={cognitiveState.capacity_percentage} 
                    className="h-3"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      {cognitiveState.energy_level}
                    </div>
                    <div className="text-xs text-gray-500">Energy</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600">
                      {cognitiveState.focus_level}
                    </div>
                    <div className="text-xs text-gray-500">Focus</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Immediate Action */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getActionIcon(cognitiveState.action_type)}
                Immediate Recommendation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <p className="text-sm font-medium text-blue-900">
                    {cognitiveState.immediate_action}
                  </p>
                </div>

                <div className="flex gap-2">
                  {cognitiveState.should_take_break && (
                    <Badge variant="destructive" className="flex items-center gap-1">
                      <AlertTriangle className="h-3 w-3" />
                      Break Needed
                    </Badge>
                  )}
                  {cognitiveState.optimal_for_complex_work && (
                    <Badge variant="default" className="flex items-center gap-1 bg-green-600">
                      <CheckCircle className="h-3 w-3" />
                      Peak Performance
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Task Recommendations */}
      {cognitiveState && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recommended Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              {cognitiveState.recommended_tasks.map((task, index) => (
                <div
                  key={index}
                  className="p-3 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
                >
                  <div className="text-sm font-medium capitalize">
                    {task.replace(/_/g, ' ')}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => {
                setEnergyLevel(Math.max(1, energyLevel - 1));
                setFocusLevel(Math.max(1, focusLevel - 1));
              }}
            >
              <Coffee className="h-5 w-5" />
              <span className="text-xs">Take Break</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => {
                setEnergyLevel(Math.min(10, energyLevel + 1));
                setFocusLevel(Math.min(10, focusLevel + 1));
              }}
            >
              <Battery className="h-5 w-5" />
              <span className="text-xs">Energize</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={performQuickCheck}
              disabled={isLoading}
            >
              <Brain className="h-5 w-5" />
              <span className="text-xs">Reassess</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => {
                setEnergyLevel(5);
                setFocusLevel(5);
              }}
            >
              <Clock className="h-5 w-5" />
              <span className="text-xs">Reset</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CognitiveLoadDashboard;
