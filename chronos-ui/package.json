{"name": "chronos-ui", "version": "1.0.0", "description": "ADHD-optimized UI for Chronos API", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "framer-motion": "^10.16.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "axios": "^1.5.0", "@tanstack/react-query": "^5.8.0", "zustand": "^4.4.0", "react-hot-toast": "^2.4.0", "lucide-react": "^0.292.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-confetti": "^6.1.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.0", "react-use-websocket": "^4.5.0", "react-calendar": "^4.6.0", "react-big-calendar": "^1.8.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-badge": "^1.0.0", "sonner": "^1.3.1"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/user-event": "^14.5.0", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}