#!/bin/bash

# Chronos Development Environment Stop Script
# This script stops all services and optionally cleans up data

set -e

echo "🛑 Stopping Chronos Development Environment..."

# Parse command line arguments
CLEAN_DATA=false
REMOVE_IMAGES=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --clean-data)
            CLEAN_DATA=true
            shift
            ;;
        --remove-images)
            REMOVE_IMAGES=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --clean-data     Remove all data volumes (databases, files, etc.)"
            echo "  --remove-images  Remove Docker images after stopping"
            echo "  --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Stop services, keep data"
            echo "  $0 --clean-data      # Stop services and remove all data"
            echo "  $0 --remove-images   # Stop services and remove images"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Stop services
echo "🔄 Stopping Docker containers..."
docker compose down

if [ "$CLEAN_DATA" = true ]; then
    echo "🧹 Removing data volumes..."
    docker compose down -v
    echo "⚠️  All data has been removed (databases, uploaded files, etc.)"
fi

if [ "$REMOVE_IMAGES" = true ]; then
    echo "🗑️  Removing Docker images..."
    docker compose down --rmi all
    echo "✅ Docker images removed"
fi

# Remove orphaned containers
echo "🧽 Cleaning up orphaned containers..."
docker compose down --remove-orphans

# Show remaining containers (if any)
remaining=$(docker ps -a --filter "name=chronos" --format "table {{.Names}}\t{{.Status}}" | tail -n +2)
if [ -n "$remaining" ]; then
    echo ""
    echo "⚠️  Some Chronos containers are still running:"
    echo "$remaining"
    echo ""
    echo "To force remove all containers:"
    echo "  docker ps -a --filter 'name=chronos' -q | xargs docker rm -f"
else
    echo "✅ All Chronos containers have been stopped and removed"
fi

# Show disk space freed (if data was cleaned)
if [ "$CLEAN_DATA" = true ]; then
    echo ""
    echo "💾 Docker system cleanup..."
    docker system prune -f
    echo "✅ Unused Docker resources cleaned up"
fi

echo ""
echo "🎉 Chronos Development Environment has been stopped!"
echo ""
echo "To start again:"
echo "  ./scripts/start-dev.sh"
echo ""
echo "To view logs from stopped containers:"
echo "  docker compose logs"
echo ""
