
"""Main FastAPI application for Project Chronos.

This module creates and configures the FastAPI application with ADHD-friendly
features, security middleware, and API routing.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1 import auth, time_tracking
from app.core.config import settings
from app.core.database import close_db, init_db
from app.core.metrics import setup_metrics


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager.

    Handles startup and shutdown events for the FastAPI application.

    Args:
        app: FastAPI application instance

    Yields:
        None during application runtime
    """
    # Startup
    if settings.DEBUG:
        await init_db()  # Initialize database in development

    yield

    # Shutdown
    await close_db()


def create_application() -> FastAPI:
    """Create and configure FastAPI application.

    Returns:
        Configured FastAPI application instance
    """
    app = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Set up Prometheus metrics
    setup_metrics(app)

    # Include API routers
    app.include_router(
        auth.router,
        prefix=f"{settings.API_V1_STR}/auth",
        tags=["authentication"],
    )

    app.include_router(
        time_tracking.router,
        prefix=f"{settings.API_V1_STR}/time-tracking",
        tags=["time-tracking", "adhd-features"],
    )

    @app.get("/")
    async def root():
        """Root endpoint with basic application information."""
        return {
            "message": "Welcome to Project Chronos",
            "description": "Neuro-affirming digital planner for ADHD users",
            "version": settings.VERSION,
            "docs": "/docs",
        }

    @app.get("/health")
    async def health_check():
        """Health check endpoint for monitoring."""
        return {"status": "healthy", "service": "project-chronos"}

    return app


# Create application instance
app = create_application()
