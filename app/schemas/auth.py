"""Authentication and authorization schemas.

This module defines Pydantic schemas for authentication-related requests and
responses, designed with ADHD-friendly validation and clear error messages.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, field_validator


class UserLogin(BaseModel):
    """Schema for user login requests.
    
    Minimal fields to reduce cognitive load during login process.
    """
    
    email: EmailStr = Field(
        ...,
        description="User's email address",
        example="<EMAIL>"
    )
    
    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="User's password",
        example="secure_password123"
    )
    
    remember_me: bool = Field(
        default=False,
        description="Whether to extend session duration"
    )


class UserCreate(BaseModel):
    """Schema for user registration with ADHD-friendly minimal requirements.
    
    Only requires essential fields to minimize registration abandonment.
    Additional profile information can be added progressively.
    """
    
    email: EmailStr = Field(
        ...,
        description="User's email address",
        example="<EMAIL>"
    )
    
    password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="User's password (minimum 8 characters)",
        example="secure_password123"
    )
    
    full_name: Optional[str] = Field(
        default=None,
        max_length=255,
        description="User's full name (optional)",
        example="Jane Doe"
    )
    
    has_adhd_diagnosis: Optional[bool] = Field(
        default=None,
        description="Whether user has ADHD diagnosis (optional, for personalization)"
    )
    
    @field_validator("password")
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password strength with clear, non-overwhelming requirements.
        
        Args:
            v: Password string to validate
            
        Returns:
            Validated password
            
        Raises:
            ValueError: If password doesn't meet requirements
        """
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        # Check for at least one letter and one number (simple but effective)
        has_letter = any(c.isalpha() for c in v)
        has_number = any(c.isdigit() for c in v)
        
        if not (has_letter and has_number):
            raise ValueError(
                "Password must contain at least one letter and one number"
            )
        
        return v
    
    @field_validator("full_name")
    @classmethod
    def validate_full_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate full name if provided.
        
        Args:
            v: Full name string to validate
            
        Returns:
            Validated full name or None
        """
        if v is not None:
            v = v.strip()
            if len(v) < 2:
                raise ValueError("Full name must be at least 2 characters long")
        return v


class TokenResponse(BaseModel):
    """Schema for authentication token responses."""
    
    access_token: str = Field(
        ...,
        description="JWT access token for API authentication"
    )
    
    refresh_token: str = Field(
        ...,
        description="JWT refresh token for obtaining new access tokens"
    )
    
    token_type: str = Field(
        default="bearer",
        description="Token type (always 'bearer')"
    )
    
    expires_in: int = Field(
        ...,
        description="Access token expiration time in seconds"
    )


class UserResponse(BaseModel):
    """Schema for user data responses (simplified).

    Excludes sensitive information like passwords and tokens.
    Matches the simplified User model schema.
    """

    id: UUID = Field(
        ...,
        description="User's unique identifier"
    )

    email: EmailStr = Field(
        ...,
        description="User's email address"
    )

    name: str = Field(
        ...,
        description="User's full name"
    )

    is_active: bool = Field(
        ...,
        description="Whether the user account is active"
    )

    is_verified: bool = Field(
        ...,
        description="Whether the user's email has been verified"
    )

    created_at: datetime = Field(
        ...,
        description="When the user account was created"
    )

    updated_at: datetime = Field(
        ...,
        description="When the user account was last updated"
    )

    last_login_at: Optional[datetime] = Field(
        default=None,
        description="When the user last logged in"
    )
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class PasswordResetRequest(BaseModel):
    """Schema for password reset requests."""
    
    email: EmailStr = Field(
        ...,
        description="Email address for password reset",
        example="<EMAIL>"
    )


class PasswordResetConfirm(BaseModel):
    """Schema for password reset confirmation."""
    
    token: str = Field(
        ...,
        description="Password reset token from email"
    )
    
    new_password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="New password"
    )
    
    @field_validator("new_password")
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        """Validate new password strength.
        
        Args:
            v: New password string to validate
            
        Returns:
            Validated password
            
        Raises:
            ValueError: If password doesn't meet requirements
        """
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        has_letter = any(c.isalpha() for c in v)
        has_number = any(c.isdigit() for c in v)
        
        if not (has_letter and has_number):
            raise ValueError(
                "Password must contain at least one letter and one number"
            )
        
        return v


class EmailVerificationRequest(BaseModel):
    """Schema for email verification requests."""
    
    email: EmailStr = Field(
        ...,
        description="Email address to verify"
    )


class EmailVerificationConfirm(BaseModel):
    """Schema for email verification confirmation."""
    
    token: str = Field(
        ...,
        description="Email verification token"
    )


class RefreshTokenRequest(BaseModel):
    """Schema for refresh token requests."""
    
    refresh_token: str = Field(
        ...,
        description="Refresh token to exchange for new access token"
    )
