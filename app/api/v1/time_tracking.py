"""
Time tracking API endpoints for ADHD time blindness support.

This module provides REST API endpoints for time estimation, tracking,
and visual progress indicators designed for ADHD users.
"""

from typing import Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.time_tracking_service import TimeTrackingService
from sqlalchemy.ext.asyncio import AsyncSession


router = APIRouter()


# Request/Response Models
class TimeEstimateRequest(BaseModel):
    """Request model for time estimation."""
    
    task_description: str = Field(
        ...,
        description="Description of the task to estimate",
        example="Write a project proposal"
    )
    
    complexity: str = Field(
        default="medium",
        description="Task complexity level",
        pattern="^(low|medium|high)$",
        example="medium"
    )

    energy_level: str = Field(
        default="medium",
        description="Current energy level",
        pattern="^(low|medium|high)$",
        example="high"
    )


class TimeEstimateResponse(BaseModel):
    """Response model for time estimation."""
    
    estimated_minutes: int = Field(
        ...,
        description="Estimated duration in minutes"
    )
    
    with_buffer_minutes: int = Field(
        ...,
        description="Estimated duration with ADHD buffer"
    )
    
    confidence_percentage: int = Field(
        ...,
        description="Confidence level in the estimate"
    )
    
    breakdown: Dict = Field(
        ...,
        description="Detailed breakdown of estimation factors"
    )


class StartTimerRequest(BaseModel):
    """Request model for starting a timer."""
    
    task_id: str = Field(
        ...,
        description="Task identifier",
        example="task_123"
    )
    
    estimated_duration: float = Field(
        ...,
        description="Estimated duration in minutes",
        gt=0,
        example=45.0
    )


class TimerStatusResponse(BaseModel):
    """Response model for timer status."""
    
    task_id: str
    elapsed_minutes: float
    remaining_minutes: float
    progress_percent: float
    phase: str
    visual_status: Dict


class TimerCompletionResponse(BaseModel):
    """Response model for timer completion."""
    
    task_id: str
    estimated_minutes: float
    actual_minutes: float
    accuracy_ratio: float
    accuracy_category: str
    feedback: str
    learning_insight: str


@router.post(
    "/estimate",
    response_model=TimeEstimateResponse,
    summary="Get time estimate for a task",
    description="Generate ADHD-optimized time estimate with confidence levels and buffers"
)
async def estimate_task_duration(
    request: TimeEstimateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> TimeEstimateResponse:
    """
    Generate a time estimate for a task with ADHD-specific considerations.
    
    This endpoint uses historical data, energy levels, and ADHD patterns
    to provide accurate time estimates with appropriate buffers.
    """
    service = TimeTrackingService(db)
    
    task_data = {
        "description": request.task_description,
        "complexity": request.complexity,
        "energy_level": request.energy_level
    }
    
    estimate = await service.create_time_estimate(current_user.id, task_data)
    
    return TimeEstimateResponse(**estimate)


@router.post(
    "/timer/start",
    response_model=Dict,
    summary="Start a task timer",
    description="Start an ADHD-friendly timer with visual progress indicators"
)
async def start_task_timer(
    request: StartTimerRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Start a timer for a task with ADHD-optimized visual cues.
    
    Provides milestone markers, color-coded progress, and motivational
    messages designed to help with time blindness.
    """
    service = TimeTrackingService(db)
    
    timer_info = await service.start_timer(
        request.task_id,
        request.estimated_duration
    )
    
    return timer_info


@router.get(
    "/timer/{task_id}/status",
    response_model=TimerStatusResponse,
    summary="Get timer status",
    description="Get current timer status with visual progress indicators"
)
async def get_timer_status(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> TimerStatusResponse:
    """
    Get the current status of a running timer.
    
    Returns progress information, visual cues, and motivational messages
    to help ADHD users stay aware of time passage.
    """
    service = TimeTrackingService(db)
    
    status = await service.get_timer_status(task_id)
    
    if not status:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No active timer found for task {task_id}"
        )
    
    return TimerStatusResponse(**status)


@router.post(
    "/timer/{task_id}/complete",
    response_model=TimerCompletionResponse,
    summary="Complete a task timer",
    description="Complete timer and get time estimation feedback"
)
async def complete_task_timer(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> TimerCompletionResponse:
    """
    Complete a task timer and receive feedback on time estimation accuracy.
    
    Provides encouraging feedback and learning insights to help improve
    future time estimation skills.
    """
    service = TimeTrackingService(db)
    
    try:
        completion_data = await service.complete_timer(task_id)
        return TimerCompletionResponse(**completion_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/timer/active",
    response_model=Dict,
    summary="Get all active timers",
    description="Get status of all active timers for the current user"
)
async def get_active_timers(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Get all active timers for the current user.
    
    Useful for dashboard views and preventing multiple timers
    for the same task.
    """
    service = TimeTrackingService(db)
    
    # This would be implemented to return all active timers
    # For now, return a placeholder
    return {
        "active_timers": [],
        "total_count": 0,
        "message": "Active timers feature coming soon!"
    }


@router.get(
    "/insights/time-patterns",
    response_model=Dict,
    summary="Get time estimation insights",
    description="Get insights about time estimation patterns and accuracy"
)
async def get_time_insights(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Get insights about the user's time estimation patterns.
    
    Provides analytics on estimation accuracy, common patterns,
    and suggestions for improvement.
    """
    # This would analyze historical data and provide insights
    # For now, return sample insights
    return {
        "overall_accuracy": "good",
        "accuracy_percentage": 78,
        "common_patterns": [
            "Tasks in the morning tend to be completed faster",
            "Complex tasks often take 20% longer than estimated",
            "Your estimation accuracy improves with practice"
        ],
        "suggestions": [
            "Consider adding 15-minute buffers to complex tasks",
            "Your focus is strongest between 9-11 AM",
            "Break large tasks into smaller, more predictable chunks"
        ],
        "recent_improvements": [
            "Time estimation accuracy improved by 15% this month",
            "Completed 85% of tasks within estimated time ranges"
        ]
    }
