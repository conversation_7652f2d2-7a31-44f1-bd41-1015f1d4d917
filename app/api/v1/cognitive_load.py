"""
Cognitive load tracking API endpoints for ADHD mental energy management.

This module provides REST API endpoints for cognitive load assessment,
mental energy tracking, and task optimization based on cognitive capacity.
"""

from typing import Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.cognitive_load_service import CognitiveLoadService, TaskComplexity
from sqlalchemy.ext.asyncio import AsyncSession


router = APIRouter()


# Request/Response Models
class TaskCognitiveLoadRequest(BaseModel):
    """Request model for task cognitive load assessment."""
    
    complexity: TaskComplexity = Field(
        default=TaskComplexity.MODERATE,
        description="Task complexity level"
    )
    
    requires_multitasking: bool = Field(
        default=False,
        description="Whether task requires multitasking"
    )
    
    requires_context_switching: bool = Field(
        default=False,
        description="Whether task requires context switching"
    )
    
    has_deadline_pressure: bool = Field(
        default=False,
        description="Whether task has deadline pressure"
    )
    
    is_creative: bool = Field(
        default=False,
        description="Whether task involves creative work"
    )
    
    interest_level: int = Field(
        default=5,
        ge=1,
        le=10,
        description="Interest level in the task (1-10)"
    )


class CognitiveStateRequest(BaseModel):
    """Request model for cognitive state assessment."""
    
    cognitive_load: int = Field(
        ...,
        ge=1,
        le=10,
        description="Current cognitive load level (1-10)"
    )
    
    energy_level: int = Field(
        ...,
        ge=1,
        le=10,
        description="Current energy level (1-10)"
    )
    
    time_of_day: str = Field(
        default="unknown",
        description="Current time of day context",
        example="morning"
    )
    
    current_task_type: str = Field(
        default="general",
        description="Type of current task",
        example="coding"
    )
    
    consecutive_high_load_hours: int = Field(
        default=0,
        ge=0,
        description="Hours of consecutive high cognitive load"
    )
    
    notes: Optional[str] = Field(
        default=None,
        description="Additional notes about current state"
    )


class CognitiveLoadResponse(BaseModel):
    """Response model for cognitive load assessment."""
    
    cognitive_load_score: int
    complexity_base: int
    adjustments: int
    load_level: str
    recommendations: list
    estimated_mental_energy_cost: Dict


class CognitiveStateResponse(BaseModel):
    """Response model for cognitive state assessment."""
    
    current_state: Dict
    capacity_analysis: Dict
    recommendations: Dict
    warnings: list
    trends: Dict


@router.post(
    "/assess-task",
    response_model=CognitiveLoadResponse,
    summary="Assess cognitive load for a task",
    description="Calculate cognitive load score and get recommendations for task execution"
)
async def assess_task_cognitive_load(
    request: TaskCognitiveLoadRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> CognitiveLoadResponse:
    """
    Assess the cognitive load required for a specific task.
    
    This endpoint analyzes task characteristics and provides a cognitive load
    score along with recommendations for optimal execution timing and approach.
    """
    service = CognitiveLoadService(db)
    
    task_data = {
        "complexity": request.complexity,
        "requires_multitasking": request.requires_multitasking,
        "requires_context_switching": request.requires_context_switching,
        "has_deadline_pressure": request.has_deadline_pressure,
        "is_creative": request.is_creative,
        "interest_level": request.interest_level
    }
    
    assessment = await service.assess_task_cognitive_load(task_data)
    
    return CognitiveLoadResponse(**assessment)


@router.post(
    "/record-state",
    response_model=CognitiveStateResponse,
    summary="Record current cognitive state",
    description="Record and analyze current cognitive load and energy levels"
)
async def record_cognitive_state(
    request: CognitiveStateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> CognitiveStateResponse:
    """
    Record the user's current cognitive state and get personalized recommendations.
    
    This endpoint helps users track their mental energy throughout the day
    and provides actionable insights for optimizing their cognitive performance.
    """
    service = CognitiveLoadService(db)
    
    context = {
        "time_of_day": request.time_of_day,
        "current_task_type": request.current_task_type,
        "consecutive_high_load_hours": request.consecutive_high_load_hours,
        "notes": request.notes
    }
    
    assessment = await service.record_cognitive_state(
        user_id=current_user.id,
        load=request.cognitive_load,
        energy=request.energy_level,
        context=context
    )
    
    return CognitiveStateResponse(**assessment)


@router.get(
    "/patterns",
    response_model=Dict,
    summary="Get cognitive patterns",
    description="Get personalized cognitive load patterns and predictions"
)
async def get_cognitive_patterns(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Get the user's cognitive load patterns and performance predictions.
    
    This endpoint analyzes historical data to identify patterns in cognitive
    performance and provides predictions for optimal task scheduling.
    """
    service = CognitiveLoadService(db)
    
    patterns = await service.get_cognitive_patterns(current_user.id)
    
    return patterns


@router.get(
    "/recommendations",
    response_model=Dict,
    summary="Get task recommendations",
    description="Get task recommendations based on current energy level"
)
async def get_task_recommendations(
    energy_level: int = Query(
        ...,
        ge=1,
        le=10,
        description="Current energy level (1-10)"
    ),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Get task recommendations based on current energy level.
    
    This endpoint provides immediate guidance on what types of tasks
    are optimal for the user's current cognitive state.
    """
    service = CognitiveLoadService(db)
    
    recommendations = await service.get_optimal_task_recommendations(
        user_id=current_user.id,
        current_energy=energy_level
    )
    
    return recommendations


@router.get(
    "/energy-forecast",
    response_model=Dict,
    summary="Get energy forecast",
    description="Get predicted energy levels for optimal planning"
)
async def get_energy_forecast(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Get energy level forecasts for the next day or week.
    
    This endpoint helps users plan their tasks by predicting when
    they'll have optimal cognitive capacity for different types of work.
    """
    service = CognitiveLoadService(db)
    
    # Get patterns which include forecasting
    patterns = await service.get_cognitive_patterns(current_user.id)
    
    return {
        "forecast": patterns.get("predictions", {}),
        "optimal_scheduling": patterns.get("predictions", {}).get("optimal_task_scheduling", {}),
        "peak_windows": patterns.get("patterns", {}).get("peak_performance_windows", []),
        "insights": patterns.get("insights", {})
    }


@router.post(
    "/quick-check",
    response_model=Dict,
    summary="Quick cognitive state check",
    description="Quick assessment for immediate task decisions"
)
async def quick_cognitive_check(
    energy: int = Query(..., ge=1, le=10, description="Current energy (1-10)"),
    focus: int = Query(..., ge=1, le=10, description="Current focus level (1-10)"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Quick cognitive state check for immediate task decisions.
    
    This endpoint provides a rapid assessment to help users make
    immediate decisions about what tasks to tackle next.
    """
    service = CognitiveLoadService(db)
    
    # Calculate available capacity
    available_capacity = max(0, min(10, energy - (10 - focus)))
    
    recommendations = await service.get_optimal_task_recommendations(
        user_id=current_user.id,
        current_energy=energy
    )
    
    # Determine immediate action
    if available_capacity <= 3:
        immediate_action = "Take a break - your cognitive capacity is low"
        action_type = "rest"
    elif available_capacity <= 6:
        immediate_action = "Good time for routine tasks and light work"
        action_type = "light_work"
    else:
        immediate_action = "Excellent time for challenging, focused work"
        action_type = "deep_work"
    
    return {
        "energy_level": energy,
        "focus_level": focus,
        "available_capacity": available_capacity,
        "capacity_percentage": (available_capacity / 10) * 100,
        "immediate_action": immediate_action,
        "action_type": action_type,
        "recommended_tasks": recommendations.get("recommended_tasks", []),
        "should_take_break": available_capacity <= 3,
        "optimal_for_complex_work": available_capacity >= 7
    }


@router.get(
    "/insights/burnout-risk",
    response_model=Dict,
    summary="Get burnout risk assessment",
    description="Assess current burnout risk based on cognitive load patterns"
)
async def get_burnout_risk_assessment(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Assess burnout risk based on recent cognitive load patterns.
    
    This endpoint analyzes recent cognitive load data to identify
    potential burnout risks and provide preventive recommendations.
    """
    # This would analyze historical data in a real implementation
    # For now, return a sample assessment
    
    return {
        "risk_level": "moderate",
        "risk_percentage": 35,
        "contributing_factors": [
            "Sustained high cognitive load for 3+ hours",
            "Limited recovery breaks",
            "Energy levels below optimal range"
        ],
        "warning_signs": [
            "Decreased task completion efficiency",
            "Increased time estimation errors",
            "More frequent frustration indicators"
        ],
        "prevention_strategies": [
            "Schedule 15-minute breaks every hour",
            "Limit complex tasks to 2-3 hours per day",
            "Prioritize sleep and recovery activities",
            "Consider delegating or postponing non-essential tasks"
        ],
        "immediate_actions": [
            "Take a 20-minute restorative break",
            "Switch to lighter, routine tasks",
            "Practice deep breathing or mindfulness"
        ],
        "recovery_timeline": "2-3 hours with proper rest and lighter workload"
    }
