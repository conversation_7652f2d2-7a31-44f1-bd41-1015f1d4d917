"""
Focus session API endpoints for ADHD-optimized productivity sessions.

This module provides REST API endpoints for managing focus sessions,
including Pomodoro, deep work, body doubling, and other ADHD-friendly formats.
"""

from typing import Dict, Optional, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.focus_session_service import FocusSessionService, FocusSessionType, InterruptionType
from sqlalchemy.ext.asyncio import AsyncSession


router = APIRouter()


# Request/Response Models
class SessionRecommendationRequest(BaseModel):
    """Request model for session recommendations."""
    
    current_energy: int = Field(
        ...,
        ge=1,
        le=10,
        description="Current energy level (1-10)"
    )
    
    task_complexity: str = Field(
        default="medium",
        pattern="^(trivial|simple|moderate|complex|expert)$",
        description="Task complexity level"
    )
    
    available_time: int = Field(
        ...,
        ge=5,
        le=240,
        description="Available time in minutes"
    )


class StartSessionRequest(BaseModel):
    """Request model for starting a focus session."""
    
    session_type: FocusSessionType = Field(
        ...,
        description="Type of focus session"
    )
    
    duration: int = Field(
        ...,
        ge=5,
        le=240,
        description="Planned duration in minutes"
    )
    
    task_description: str = Field(
        default="",
        max_length=500,
        description="Description of work to be done"
    )


@router.post(
    "/recommendations",
    response_model=Dict,
    summary="Get focus session recommendations",
    description="Get personalized focus session type recommendations based on current state"
)
async def get_session_recommendations(
    request: SessionRecommendationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Get personalized focus session recommendations.
    
    This endpoint analyzes your current energy level, task complexity,
    and available time to recommend the optimal focus session type.
    """
    service = FocusSessionService(db)
    
    recommendations = await service.get_session_recommendations(
        user_id=current_user.id,
        current_energy=request.current_energy,
        task_complexity=request.task_complexity,
        available_time=request.available_time
    )
    
    return recommendations


@router.post(
    "/start",
    response_model=Dict,
    summary="Start a focus session",
    description="Start a new ADHD-optimized focus session with guidance and tracking"
)
async def start_focus_session(
    request: StartSessionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Start a new focus session.
    
    This endpoint creates a new focus session with ADHD-friendly features
    including interruption handling, flexible timing, and motivational guidance.
    """
    service = FocusSessionService(db)
    
    session_info = await service.start_focus_session(
        user_id=current_user.id,
        session_type=request.session_type.value,
        duration=request.duration,
        task_description=request.task_description
    )
    
    return session_info


@router.get(
    "/active",
    response_model=Dict,
    summary="Get active sessions",
    description="Get all currently active focus sessions for the user"
)
async def get_active_sessions(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Get all active focus sessions for the current user.
    
    This endpoint returns information about any currently running
    focus sessions, useful for dashboard views and session management.
    """
    service = FocusSessionService(db)
    
    active_sessions = await service.get_active_sessions(current_user.id)
    
    return active_sessions


@router.get(
    "/types",
    response_model=Dict,
    summary="Get session types",
    description="Get information about all available focus session types"
)
async def get_session_types(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Get information about all available focus session types.
    
    This endpoint provides details about each session type,
    helping users understand their options and make informed choices.
    """
    service = FocusSessionService(db)
    
    # Get session templates from the manager
    templates = service.manager.session_templates
    
    session_types = {}
    for session_type, template in templates.items():
        session_types[session_type.value] = {
            "name": session_type.value.replace("_", " ").title(),
            "default_duration": template["default_duration"],
            "break_duration": template["break_duration"],
            "flexibility": template["flexibility"],
            "interruption_tolerance": template["interruption_tolerance"],
            "description": template["description"],
            "best_for": get_session_best_for(session_type)
        }
    
    return {
        "session_types": session_types,
        "total_types": len(session_types)
    }


@router.post(
    "/{session_id}/complete",
    response_model=Dict,
    summary="Complete a focus session",
    description="Complete a focus session and get insights and achievements"
)
async def complete_focus_session(
    session_id: str,
    productivity_rating: Optional[int] = Query(
        default=None,
        ge=1,
        le=10,
        description="Productivity rating (1-10)"
    ),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> Dict:
    """
    Complete a focus session.
    
    This endpoint finalizes the session and provides insights,
    achievements, and recommendations for future sessions.
    """
    service = FocusSessionService(db)
    
    try:
        completion_data = await service.complete_session(
            session_id=session_id,
            productivity_rating=productivity_rating,
            notes=""
        )
        return completion_data
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


def get_session_best_for(session_type: FocusSessionType) -> List[str]:
    """Get what each session type is best for."""
    best_for = {
        FocusSessionType.POMODORO: [
            "Routine tasks",
            "Building focus habits",
            "Moderate energy levels",
            "Time-boxed work"
        ],
        FocusSessionType.MICRO_FOCUS: [
            "Low energy periods",
            "Overwhelming tasks",
            "Getting started",
            "Building momentum"
        ],
        FocusSessionType.DEEP_WORK: [
            "Complex problems",
            "High energy periods",
            "Creative projects",
            "Learning new skills"
        ],
        FocusSessionType.BODY_DOUBLING: [
            "Accountability needs",
            "Boring tasks",
            "Social motivation",
            "Procrastination struggles"
        ],
        FocusSessionType.CREATIVE_FLOW: [
            "Artistic work",
            "Open-ended projects",
            "Innovation tasks",
            "Hyperfocus periods"
        ],
        FocusSessionType.TASK_SPRINT: [
            "Quick completions",
            "Urgent deadlines",
            "Small tasks",
            "Momentum building"
        ],
        FocusSessionType.GENTLE_FOCUS: [
            "Difficult days",
            "High stress periods",
            "Recovery sessions",
            "Flexible scheduling"
        ]
    }
    
    return best_for.get(session_type, ["General focused work"])
