"""Authentication service with ADHD-friendly features.

This module provides authentication business logic including user registration,
login, password reset, and email verification with considerations for ADHD users
such as reasonable timeouts and clear error messages.
"""

from datetime import datetime, timedelta, timezone
from typing import Optional, Tuple
from uuid import UUID

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import security
from app.models.user import User
from app.schemas.auth import UserCreate, UserLogin


class AuthenticationError(Exception):
    """Base exception for authentication errors."""
    pass


class InvalidCredentialsError(AuthenticationError):
    """Raised when login credentials are invalid."""
    pass


class AccountLockedError(AuthenticationError):
    """Raised when account is locked due to failed login attempts."""
    pass


class EmailNotVerifiedError(AuthenticationError):
    """Raised when attempting to login with unverified email."""
    pass


class AuthService:
    """Authentication service with ADHD-friendly features.
    
    Provides secure authentication while being mindful of ADHD-specific needs
    like reasonable session timeouts and clear, non-overwhelming error messages.
    """
    
    def __init__(self, db: AsyncSession) -> None:
        """Initialize authentication service.
        
        Args:
            db: Database session for user operations
        """
        self.db = db
        self.max_failed_attempts = 5
        self.lockout_duration_minutes = 30  # Reasonable for ADHD users
    
    async def register_user(self, user_data: UserCreate) -> User:
        """Register new user with ADHD-friendly minimal requirements.
        
        Args:
            user_data: User registration data
            
        Returns:
            Created user instance
            
        Raises:
            ValueError: If email already exists or validation fails
        """
        # Check if email already exists
        existing_user = await self.get_user_by_email(user_data.email)
        if existing_user:
            raise ValueError("Email address is already registered")
        
        # Create new user
        user = User(
            email=user_data.email,
            password_hash=security.hash_password(user_data.password),
            name=user_data.full_name or user_data.email.split("@")[0],
            is_active=True,
            is_verified=False,  # Require email verification
        )
        
        # Note: Profile completion tracking removed for simplified model
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
    
    async def authenticate_user(
        self, 
        credentials: UserLogin
    ) -> Tuple[str, str, User]:
        """Authenticate user and return tokens.
        
        Args:
            credentials: User login credentials
            
        Returns:
            Tuple of (access_token, refresh_token, user)
            
        Raises:
            InvalidCredentialsError: If credentials are invalid
            AccountLockedError: If account is locked
            EmailNotVerifiedError: If email is not verified
        """
        user = await self.get_user_by_email(credentials.email)
        
        if not user:
            raise InvalidCredentialsError("Invalid email or password")
        
        # Note: Account locking removed for simplified model
        
        # Verify password
        if not security.verify_password(credentials.password, user.password_hash):
            raise InvalidCredentialsError("Invalid email or password")
        
        # Check if email is verified (optional for ADHD users to reduce friction)
        # if not user.is_verified:
        #     raise EmailNotVerifiedError("Please verify your email address first")
        
        # Update last login timestamp
        user.last_login_at = datetime.now(timezone.utc)
        await self.db.commit()

        # Refresh user to avoid session expiration issues
        await self.db.refresh(user)

        # Generate tokens
        token_data = {"sub": str(user.id), "email": user.email}
        
        # Extend refresh token if "remember me" is checked
        if credentials.remember_me:
            refresh_expires = timedelta(days=30)  # Extended for ADHD users
        else:
            refresh_expires = None
        
        access_token = security.create_access_token(token_data)
        refresh_token = security.create_refresh_token(token_data, refresh_expires)
        
        return access_token, refresh_token, user
    
    async def refresh_access_token(self, refresh_token: str) -> Tuple[str, str]:
        """Refresh access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            Tuple of (new_access_token, new_refresh_token)
            
        Raises:
            InvalidCredentialsError: If refresh token is invalid
        """
        payload = security.verify_token(refresh_token, "refresh")
        if not payload:
            raise InvalidCredentialsError("Invalid refresh token")
        
        user_id = payload.get("sub")
        if not user_id:
            raise InvalidCredentialsError("Invalid refresh token")
        
        # Verify user still exists and is active
        user = await self.get_user_by_id(UUID(user_id))
        if not user or not user.is_active:
            raise InvalidCredentialsError("User account is inactive")
        
        # Generate new tokens
        token_data = {"sub": str(user.id), "email": user.email}
        new_access_token = security.create_access_token(token_data)
        new_refresh_token = security.create_refresh_token(token_data)
        
        return new_access_token, new_refresh_token
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address.
        
        Args:
            email: User's email address
            
        Returns:
            User instance if found, None otherwise
        """
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get user by ID.
        
        Args:
            user_id: User's unique identifier
            
        Returns:
            User instance if found, None otherwise
        """
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def _handle_failed_login(self, user: User) -> None:
        """Handle failed login attempt with progressive lockout.
        
        Args:
            user: User who failed to login
            
        Note:
            Implements progressive lockout that's not too harsh for ADHD users
            who might genuinely forget passwords frequently.
        """
        user.failed_login_attempts += 1
        
        # Lock account after max attempts
        if user.failed_login_attempts >= self.max_failed_attempts:
            user.account_locked_until = (
                datetime.now(timezone.utc) + timedelta(minutes=self.lockout_duration_minutes)
            )
        
        await self.db.commit()
    
    async def _reset_failed_login_attempts(self, user: User) -> None:
        """Reset failed login attempts after successful login.
        
        Args:
            user: User who successfully logged in
        """
        if user.failed_login_attempts > 0 or user.account_locked_until:
            user.failed_login_attempts = 0
            user.account_locked_until = None
            await self.db.commit()
