"""
Cognitive load tracking service for ADHD mental energy management.

This service helps ADHD users monitor their cognitive load, track mental energy
patterns, and make informed decisions about task scheduling and workload.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from uuid import UUID
import asyncio
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from pydantic import BaseModel

from app.models.user import User
from app.models.task import Task
from app.core.metrics import metrics_collector


class CognitiveLoadLevel(str, Enum):
    """Cognitive load levels for ADHD users."""
    VERY_LOW = "very_low"      # 1-2: Minimal mental effort
    LOW = "low"                # 3-4: Light cognitive work
    MODERATE = "moderate"      # 5-6: Standard mental effort
    HIGH = "high"              # 7-8: Intensive cognitive work
    VERY_HIGH = "very_high"    # 9-10: Maximum mental capacity


class EnergyLevel(str, Enum):
    """Energy levels throughout the day."""
    DEPLETED = "depleted"      # 1-2: Exhausted, need rest
    LOW = "low"                # 3-4: Tired, limited capacity
    MODERATE = "moderate"      # 5-6: Normal energy
    HIGH = "high"              # 7-8: Good energy, productive
    PEAK = "peak"              # 9-10: Optimal performance state


class TaskComplexity(str, Enum):
    """Task complexity levels for cognitive load calculation."""
    TRIVIAL = "trivial"        # Routine, automatic tasks
    SIMPLE = "simple"          # Basic tasks requiring minimal focus
    MODERATE = "moderate"      # Standard tasks requiring attention
    COMPLEX = "complex"        # Challenging tasks requiring deep focus
    EXPERT = "expert"          # Highly complex, expert-level tasks


class CognitiveLoadAssessment(BaseModel):
    """Model for cognitive load assessment data."""
    user_id: UUID
    timestamp: datetime
    cognitive_load: int  # 1-10 scale
    energy_level: int    # 1-10 scale
    task_type: str
    context: Dict
    notes: Optional[str] = None


class CognitiveLoadTracker:
    """Core cognitive load tracking functionality."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.load_history: List[CognitiveLoadAssessment] = []
    
    def calculate_task_cognitive_load(self, task_data: Dict) -> Dict:
        """
        Calculate the cognitive load score for a task.
        
        Args:
            task_data: Task information including complexity, type, etc.
            
        Returns:
            Cognitive load analysis with score and recommendations
        """
        # Base cognitive load by task complexity
        complexity_scores = {
            TaskComplexity.TRIVIAL: 1,
            TaskComplexity.SIMPLE: 3,
            TaskComplexity.MODERATE: 5,
            TaskComplexity.COMPLEX: 7,
            TaskComplexity.EXPERT: 9
        }
        
        complexity = task_data.get("complexity", TaskComplexity.MODERATE)
        base_score = complexity_scores.get(complexity, 5)
        
        # Adjust for task characteristics
        adjustments = 0
        
        # Multi-tasking penalty
        if task_data.get("requires_multitasking", False):
            adjustments += 2
        
        # Context switching penalty
        if task_data.get("requires_context_switching", False):
            adjustments += 1
        
        # Time pressure penalty
        if task_data.get("has_deadline_pressure", False):
            adjustments += 1
        
        # Creative work bonus (can be energizing for ADHD)
        if task_data.get("is_creative", False):
            adjustments -= 1
        
        # Interest level adjustment
        interest_level = task_data.get("interest_level", 5)  # 1-10 scale
        if interest_level >= 8:
            adjustments -= 1  # High interest reduces cognitive load
        elif interest_level <= 3:
            adjustments += 1  # Low interest increases cognitive load
        
        # Calculate final score
        final_score = max(1, min(10, base_score + adjustments))
        
        return {
            "cognitive_load_score": final_score,
            "complexity_base": base_score,
            "adjustments": adjustments,
            "load_level": self._score_to_load_level(final_score),
            "recommendations": self._generate_load_recommendations(final_score, task_data),
            "estimated_mental_energy_cost": self._calculate_energy_cost(final_score)
        }
    
    def assess_current_cognitive_state(self, user_id: UUID, 
                                     current_load: int, 
                                     energy_level: int,
                                     context: Dict) -> Dict:
        """
        Assess the user's current cognitive state and capacity.
        
        Args:
            user_id: User identifier
            current_load: Current cognitive load (1-10)
            energy_level: Current energy level (1-10)
            context: Additional context (time of day, recent activities, etc.)
            
        Returns:
            Cognitive state assessment with recommendations
        """
        # Record the assessment
        assessment = CognitiveLoadAssessment(
            user_id=user_id,
            timestamp=datetime.utcnow(),
            cognitive_load=current_load,
            energy_level=energy_level,
            task_type=context.get("current_task_type", "general"),
            context=context
        )
        
        self.load_history.append(assessment)
        
        # Calculate cognitive capacity
        available_capacity = self._calculate_available_capacity(current_load, energy_level)
        
        # Determine optimal task types
        optimal_tasks = self._recommend_optimal_tasks(available_capacity, energy_level)
        
        # Check for warning signs
        warnings = self._check_cognitive_warnings(current_load, energy_level, context)
        
        # Record metrics
        time_of_day = context.get("time_of_day", "unknown")
        task_type = context.get("current_task_type", "general")
        metrics_collector.record_cognitive_load(current_load, time_of_day, task_type)
        
        return {
            "current_state": {
                "cognitive_load": current_load,
                "energy_level": energy_level,
                "load_level": self._score_to_load_level(current_load),
                "energy_state": self._score_to_energy_level(energy_level)
            },
            "capacity_analysis": {
                "available_capacity": available_capacity,
                "capacity_percentage": (available_capacity / 10) * 100,
                "can_handle_complex_tasks": available_capacity >= 6,
                "should_take_break": available_capacity <= 3
            },
            "recommendations": {
                "optimal_task_types": optimal_tasks,
                "suggested_actions": self._suggest_actions(available_capacity),
                "break_recommendation": self._recommend_break_type(current_load, energy_level)
            },
            "warnings": warnings,
            "trends": self._analyze_recent_trends(user_id)
        }
    
    def predict_cognitive_patterns(self, user_id: UUID) -> Dict:
        """
        Predict cognitive load patterns based on historical data.
        
        Args:
            user_id: User identifier
            
        Returns:
            Predicted patterns and optimal scheduling suggestions
        """
        # Get historical data (would query database in real implementation)
        historical_data = self._get_historical_cognitive_data(user_id)
        
        if not historical_data:
            return self._default_cognitive_patterns()
        
        # Analyze patterns by time of day
        hourly_patterns = self._analyze_hourly_patterns(historical_data)
        
        # Analyze patterns by day of week
        daily_patterns = self._analyze_daily_patterns(historical_data)
        
        # Identify peak performance windows
        peak_windows = self._identify_peak_windows(hourly_patterns)
        
        # Generate scheduling recommendations
        scheduling_advice = self._generate_scheduling_advice(peak_windows, daily_patterns)
        
        return {
            "patterns": {
                "hourly_energy": hourly_patterns,
                "daily_trends": daily_patterns,
                "peak_performance_windows": peak_windows
            },
            "predictions": {
                "next_peak_window": self._predict_next_peak(peak_windows),
                "energy_forecast": self._forecast_energy_levels(),
                "optimal_task_scheduling": scheduling_advice
            },
            "insights": {
                "best_hours_for_complex_tasks": self._find_best_complex_task_hours(hourly_patterns),
                "natural_break_times": self._find_natural_break_times(hourly_patterns),
                "energy_recovery_patterns": self._analyze_recovery_patterns(historical_data)
            }
        }
    
    def _score_to_load_level(self, score: int) -> CognitiveLoadLevel:
        """Convert numeric score to cognitive load level."""
        if score <= 2:
            return CognitiveLoadLevel.VERY_LOW
        elif score <= 4:
            return CognitiveLoadLevel.LOW
        elif score <= 6:
            return CognitiveLoadLevel.MODERATE
        elif score <= 8:
            return CognitiveLoadLevel.HIGH
        else:
            return CognitiveLoadLevel.VERY_HIGH
    
    def _score_to_energy_level(self, score: int) -> EnergyLevel:
        """Convert numeric score to energy level."""
        if score <= 2:
            return EnergyLevel.DEPLETED
        elif score <= 4:
            return EnergyLevel.LOW
        elif score <= 6:
            return EnergyLevel.MODERATE
        elif score <= 8:
            return EnergyLevel.HIGH
        else:
            return EnergyLevel.PEAK
    
    def _calculate_available_capacity(self, current_load: int, energy_level: int) -> int:
        """Calculate available cognitive capacity."""
        # Available capacity is roughly: energy_level - current_load
        # But with ADHD-specific adjustments
        base_capacity = energy_level - current_load
        
        # ADHD users often have less linear capacity
        if energy_level <= 3:  # Low energy
            base_capacity = max(0, base_capacity - 1)
        elif energy_level >= 8:  # High energy
            base_capacity = min(10, base_capacity + 1)
        
        return max(0, min(10, base_capacity))
    
    def _recommend_optimal_tasks(self, available_capacity: int, energy_level: int) -> List[str]:
        """Recommend optimal task types based on current capacity."""
        if available_capacity >= 7:
            return ["complex_analysis", "creative_work", "learning_new_skills", "strategic_planning"]
        elif available_capacity >= 5:
            return ["routine_tasks", "email_processing", "documentation", "light_coding"]
        elif available_capacity >= 3:
            return ["simple_tasks", "organizing", "light_reading", "basic_admin"]
        else:
            return ["rest", "light_stretching", "mindful_breathing", "gentle_movement"]
    
    def _generate_load_recommendations(self, score: int, task_data: Dict) -> List[str]:
        """Generate recommendations based on cognitive load score."""
        recommendations = []
        
        if score >= 8:
            recommendations.extend([
                "Consider breaking this task into smaller chunks",
                "Schedule during your peak energy hours",
                "Eliminate distractions before starting",
                "Plan for breaks every 25-30 minutes"
            ])
        elif score >= 6:
            recommendations.extend([
                "Good candidate for focused work sessions",
                "Consider using the Pomodoro technique",
                "Ensure you're well-rested before starting"
            ])
        else:
            recommendations.extend([
                "Great task for lower energy periods",
                "Can be done alongside other light activities",
                "Good for building momentum"
            ])
        
        return recommendations
    
    def _calculate_energy_cost(self, cognitive_load: int) -> Dict:
        """Calculate estimated mental energy cost."""
        # Energy cost increases exponentially with cognitive load
        base_cost = cognitive_load * 10  # Base percentage
        
        # ADHD users often experience higher energy costs
        adhd_multiplier = 1.2
        
        total_cost = base_cost * adhd_multiplier
        
        return {
            "percentage_of_daily_energy": min(100, total_cost),
            "recovery_time_minutes": cognitive_load * 5,
            "recommended_break_after": max(30, 90 - (cognitive_load * 10))
        }
    
    def _check_cognitive_warnings(self, load: int, energy: int, context: Dict) -> List[Dict]:
        """Check for cognitive overload warning signs."""
        warnings = []
        
        if load >= 8 and energy <= 4:
            warnings.append({
                "type": "overload_risk",
                "severity": "high",
                "message": "High cognitive load with low energy - risk of burnout",
                "suggestion": "Consider postponing complex tasks or taking a longer break"
            })
        
        if load >= 6 and context.get("consecutive_high_load_hours", 0) >= 3:
            warnings.append({
                "type": "sustained_load",
                "severity": "medium",
                "message": "Sustained high cognitive load detected",
                "suggestion": "Schedule a proper break or switch to lighter tasks"
            })
        
        return warnings
    
    def _suggest_actions(self, available_capacity: int) -> List[str]:
        """Suggest immediate actions based on available capacity."""
        if available_capacity <= 2:
            return [
                "Take a 15-20 minute break",
                "Do some light stretching or movement",
                "Practice deep breathing exercises",
                "Consider a short walk outside"
            ]
        elif available_capacity <= 4:
            return [
                "Switch to lighter, routine tasks",
                "Organize your workspace",
                "Review and plan upcoming tasks",
                "Take micro-breaks every 15 minutes"
            ]
        else:
            return [
                "Good time for focused work",
                "Tackle challenging tasks now",
                "Use time-blocking for deep work",
                "Minimize interruptions"
            ]
    
    def _recommend_break_type(self, load: int, energy: int) -> Dict:
        """Recommend appropriate break type."""
        if energy <= 3:
            return {
                "type": "restorative",
                "duration": "15-30 minutes",
                "activities": ["rest", "meditation", "gentle_stretching"],
                "avoid": ["screens", "stimulating_activities"]
            }
        elif load >= 7:
            return {
                "type": "cognitive_reset",
                "duration": "10-15 minutes",
                "activities": ["walk", "fresh_air", "mindful_breathing"],
                "avoid": ["complex_thinking", "decision_making"]
            }
        else:
            return {
                "type": "active_recovery",
                "duration": "5-10 minutes",
                "activities": ["light_movement", "hydration", "brief_social_interaction"],
                "avoid": ["sitting_still", "continued_screen_time"]
            }
    
    def _analyze_recent_trends(self, user_id: UUID) -> Dict:
        """Analyze recent cognitive load trends."""
        # Would analyze last 7 days of data in real implementation
        return {
            "trend_direction": "stable",
            "average_load": 5.2,
            "peak_times": ["9:00-11:00", "14:00-16:00"],
            "low_energy_periods": ["13:00-14:00", "16:00-17:00"]
        }
    
    def _get_historical_cognitive_data(self, user_id: UUID) -> List[Dict]:
        """Get historical cognitive load data (mock implementation)."""
        # In real implementation, this would query the database
        return []
    
    def _default_cognitive_patterns(self) -> Dict:
        """Return default cognitive patterns for new users."""
        return {
            "patterns": {
                "hourly_energy": {
                    "9": 7, "10": 8, "11": 7, "12": 6,
                    "13": 4, "14": 6, "15": 7, "16": 5
                },
                "daily_trends": {
                    "monday": 6, "tuesday": 7, "wednesday": 6,
                    "thursday": 5, "friday": 4
                },
                "peak_performance_windows": ["9:00-11:00", "14:00-16:00"]
            },
            "predictions": {
                "next_peak_window": "Tomorrow 9:00-11:00",
                "optimal_task_scheduling": "Schedule complex tasks in morning hours"
            }
        }
    
    def _analyze_hourly_patterns(self, data: List[Dict]) -> Dict:
        """Analyze energy patterns by hour of day."""
        # Mock implementation
        return {
            "9": 7.2, "10": 8.1, "11": 7.8, "12": 6.5,
            "13": 4.2, "14": 6.8, "15": 7.1, "16": 5.9
        }
    
    def _analyze_daily_patterns(self, data: List[Dict]) -> Dict:
        """Analyze energy patterns by day of week."""
        return {
            "monday": 6.8, "tuesday": 7.2, "wednesday": 6.9,
            "thursday": 6.1, "friday": 5.4
        }
    
    def _identify_peak_windows(self, hourly_patterns: Dict) -> List[str]:
        """Identify peak performance time windows."""
        return ["9:00-11:00", "14:00-16:00"]
    
    def _generate_scheduling_advice(self, peak_windows: List[str], daily_patterns: Dict) -> Dict:
        """Generate scheduling advice based on patterns."""
        return {
            "complex_tasks": "Schedule during peak windows: " + ", ".join(peak_windows),
            "routine_tasks": "Best for low-energy periods: 13:00-14:00",
            "creative_work": "Optimal during high-energy periods",
            "meetings": "Avoid during natural low-energy times"
        }
    
    def _predict_next_peak(self, peak_windows: List[str]) -> str:
        """Predict the next peak performance window."""
        return "Tomorrow 9:00-11:00"
    
    def _forecast_energy_levels(self) -> Dict:
        """Forecast energy levels for the next day."""
        return {
            "morning": "high",
            "afternoon": "moderate",
            "evening": "low"
        }
    
    def _find_best_complex_task_hours(self, hourly_patterns: Dict) -> List[str]:
        """Find the best hours for complex tasks."""
        return ["9:00-11:00", "14:00-16:00"]
    
    def _find_natural_break_times(self, hourly_patterns: Dict) -> List[str]:
        """Find natural break times based on energy dips."""
        return ["13:00-14:00", "16:30-17:00"]
    
    def _analyze_recovery_patterns(self, data: List[Dict]) -> Dict:
        """Analyze how quickly user recovers from high cognitive load."""
        return {
            "average_recovery_time": "20 minutes",
            "best_recovery_activities": ["short_walk", "deep_breathing"],
            "recovery_efficiency": "good"
        }


class CognitiveLoadService:
    """Main service for cognitive load tracking."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.tracker = CognitiveLoadTracker(db)
    
    async def assess_task_cognitive_load(self, task_data: Dict) -> Dict:
        """Assess cognitive load for a task."""
        return self.tracker.calculate_task_cognitive_load(task_data)
    
    async def record_cognitive_state(self, user_id: UUID, load: int, 
                                   energy: int, context: Dict) -> Dict:
        """Record and assess current cognitive state."""
        return self.tracker.assess_current_cognitive_state(user_id, load, energy, context)
    
    async def get_cognitive_patterns(self, user_id: UUID) -> Dict:
        """Get cognitive load patterns and predictions."""
        return self.tracker.predict_cognitive_patterns(user_id)

    async def get_optimal_task_recommendations(self, user_id: UUID,
                                             current_energy: int) -> Dict:
        """Get task recommendations based on current cognitive state."""
        available_capacity = max(0, current_energy - 3)  # Assume moderate current load
        optimal_tasks = self.tracker._recommend_optimal_tasks(available_capacity, current_energy)

        return {
            "current_energy": current_energy,
            "available_capacity": available_capacity,
            "recommended_tasks": optimal_tasks,
            "capacity_percentage": (available_capacity / 10) * 100,
            "suggestions": self.tracker._suggest_actions(available_capacity)
        }
