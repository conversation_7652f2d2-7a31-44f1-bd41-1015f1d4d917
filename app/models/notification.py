"""
Notification models for Project Chronos.

This module defines SQLAlchemy models for ADHD-optimized notifications,
reminders, and background task management.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import (
    String, Integer, Boolean, DateTime, Text, JSON, Float,
    ForeignKey, UniqueConstraint, Index, Enum as SQLEnum
)
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.models.base import Base


class NotificationType(str, Enum):
    """Types of notifications."""
    TASK_REMINDER = "task_reminder"
    DEADLINE_WARNING = "deadline_warning"
    FOCUS_BREAK = "focus_break"
    BODY_DOUBLING_INVITE = "body_doubling_invite"
    ACHIEVEMENT = "achievement"
    DAILY_REVIEW = "daily_review"
    SCHEDULE_CONFLICT = "schedule_conflict"
    ENERGY_CHECK = "energy_check"
    MEDICATION_REMINDER = "medication_reminder"
    HABIT_REMINDER = "habit_reminder"


class NotificationPriority(str, Enum):
    """Notification priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class NotificationStatus(str, Enum):
    """Notification status states."""
    SCHEDULED = "scheduled"
    SENT = "sent"
    DELIVERED = "delivered"
    ACKNOWLEDGED = "acknowledged"
    SNOOZED = "snoozed"
    EXPIRED = "expired"
    CANCELLED = "cancelled"


class Notification(Base):
    """
    Notification model for ADHD-optimized reminders and alerts.
    
    Supports persistent reminders, staggered delivery, and
    context-aware timing for ADHD attention patterns.
    """
    
    __tablename__ = "notifications"
    
    # User association
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="User who will receive this notification"
    )
    
    # Optional associations
    task_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Associated task (optional)"
    )
    
    time_block_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("time_blocks.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Associated time block (optional)"
    )
    
    focus_session_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("focus_sessions.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Associated focus session (optional)"
    )
    
    # Notification content
    type: Mapped[NotificationType] = mapped_column(
        SQLEnum(NotificationType),
        nullable=False,
        index=True,
        doc="Type of notification"
    )
    
    title: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Notification title"
    )
    
    message: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Notification message content"
    )
    
    action_text: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="Text for primary action button"
    )
    
    action_url: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        doc="URL for primary action"
    )
    
    # Scheduling and delivery
    scheduled_for: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="When notification should be delivered"
    )
    
    priority: Mapped[NotificationPriority] = mapped_column(
        SQLEnum(NotificationPriority),
        nullable=False,
        default=NotificationPriority.NORMAL,
        doc="Notification priority level"
    )
    
    delivery_channels: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        doc="Delivery channels (push, email, sms)"
    )
    
    # ADHD-specific features
    is_persistent: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether notification requires acknowledgment"
    )
    
    respect_focus_mode: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether to defer during focus sessions"
    )
    
    max_snooze_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=3,
        doc="Maximum number of times notification can be snoozed"
    )
    
    snooze_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of times notification has been snoozed"
    )
    
    # Status tracking
    status: Mapped[NotificationStatus] = mapped_column(
        SQLEnum(NotificationStatus),
        nullable=False,
        default=NotificationStatus.SCHEDULED,
        doc="Current notification status"
    )
    
    sent_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When notification was sent"
    )
    
    delivered_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When notification was delivered"
    )
    
    acknowledged_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When notification was acknowledged"
    )
    
    acknowledgment_action: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        doc="Action taken when acknowledging"
    )
    
    snoozed_until: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When snoozed notification should reappear"
    )
    
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When notification expires"
    )
    
    # Delivery tracking
    delivery_attempts: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of delivery attempts"
    )
    
    last_delivery_attempt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When last delivery was attempted"
    )
    
    delivery_errors: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        doc="Delivery error messages"
    )
    
    # Metadata
    notification_data: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Additional notification metadata"
    )
    
    # Background task tracking
    celery_task_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Celery task ID for scheduled delivery"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="notifications",
        doc="User who will receive this notification"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="notifications",
        doc="Associated task"
    )
    
    time_block: Mapped[Optional["TimeBlock"]] = relationship(
        "TimeBlock",
        back_populates="notifications",
        doc="Associated time block"
    )
    
    focus_session: Mapped[Optional["FocusSession"]] = relationship(
        "FocusSession",
        back_populates="notifications",
        doc="Associated focus session"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_notifications_user", "user_id"),
        Index("idx_notifications_scheduled", "scheduled_for"),
        Index("idx_notifications_status", "status"),
        Index("idx_notifications_type", "type"),
        Index("idx_notifications_priority", "priority"),
        Index("idx_notifications_task", "task_id"),
        Index("idx_notifications_persistent", "is_persistent"),
        Index("idx_notifications_celery_task", "celery_task_id"),
    )
    
    def is_overdue(self) -> bool:
        """Check if notification is overdue for delivery."""
        return (
            self.status == NotificationStatus.SCHEDULED and
            self.scheduled_for < datetime.utcnow()
        )
    
    def can_be_snoozed(self) -> bool:
        """Check if notification can be snoozed."""
        return (
            self.status in [NotificationStatus.SENT, NotificationStatus.DELIVERED] and
            self.snooze_count < self.max_snooze_count
        )
    
    def is_expired(self) -> bool:
        """Check if notification has expired."""
        return (
            self.expires_at is not None and
            self.expires_at < datetime.utcnow()
        )
    
    def get_next_escalation_time(self) -> Optional[datetime]:
        """Get when notification should be escalated if not acknowledged."""
        if not self.is_persistent or self.acknowledged_at:
            return None
        
        if self.sent_at:
            # Escalate after 5, 15, 30 minutes
            escalation_intervals = [5, 15, 30]
            attempt = min(self.delivery_attempts, len(escalation_intervals) - 1)
            return self.sent_at + timedelta(minutes=escalation_intervals[attempt])
        
        return None


class NotificationPreference(Base):
    """
    User preferences for notification delivery and timing.
    
    Stores ADHD-specific preferences for when and how
    notifications should be delivered.
    """
    
    __tablename__ = "notification_preferences"
    
    # User association (one-to-one)
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        doc="User these preferences belong to"
    )
    
    # General preferences
    enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether notifications are enabled"
    )
    
    quiet_hours_start: Mapped[Optional[str]] = mapped_column(
        String(5),  # HH:MM format
        nullable=True,
        doc="Start of quiet hours (no notifications)"
    )
    
    quiet_hours_end: Mapped[Optional[str]] = mapped_column(
        String(5),  # HH:MM format
        nullable=True,
        doc="End of quiet hours"
    )
    
    # Channel preferences
    push_enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Enable push notifications"
    )
    
    email_enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Enable email notifications"
    )
    
    sms_enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Enable SMS notifications"
    )
    
    # ADHD-specific preferences
    persistent_reminders: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Enable persistent reminders that require acknowledgment"
    )
    
    respect_focus_mode: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Defer non-urgent notifications during focus sessions"
    )
    
    batch_non_urgent: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Batch non-urgent notifications for designated times"
    )
    
    batch_times: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=lambda: ["09:00", "13:00", "17:00"],
        doc="Times for batched notification delivery"
    )
    
    # Escalation preferences
    escalation_enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Enable notification escalation for unacknowledged reminders"
    )
    
    escalation_channels: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=lambda: ["push", "email"],
        doc="Channels to use for escalation"
    )
    
    max_escalation_attempts: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=3,
        doc="Maximum escalation attempts"
    )
    
    # Type-specific preferences
    type_preferences: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Preferences for specific notification types"
    )
    
    # Additional settings
    settings: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Additional notification preferences"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="notification_preferences",
        doc="User these preferences belong to"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_notification_preferences_user", "user_id"),
    )


class NotificationTemplate(Base):
    """
    Templates for generating consistent, ADHD-friendly notifications.
    
    Provides reusable templates with clear, actionable content
    that reduces cognitive load for ADHD users.
    """
    
    __tablename__ = "notification_templates"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique template identifier"
    )
    
    # Template identification
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        unique=True,
        doc="Template name"
    )
    
    type: Mapped[NotificationType] = mapped_column(
        SQLEnum(NotificationType),
        nullable=False,
        index=True,
        doc="Notification type this template is for"
    )
    
    # Template content
    title_template: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Title template with placeholders"
    )
    
    message_template: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Message template with placeholders"
    )
    
    action_text_template: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="Action button text template"
    )
    
    # Template configuration
    default_priority: Mapped[NotificationPriority] = mapped_column(
        SQLEnum(NotificationPriority),
        nullable=False,
        default=NotificationPriority.NORMAL,
        doc="Default priority for this template"
    )
    
    default_channels: Mapped[List[str]] = mapped_column(
        JSONB,
        nullable=False,
        default=lambda: ["push"],
        doc="Default delivery channels"
    )
    
    is_persistent: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether notifications from this template are persistent"
    )
    
    # Template metadata
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Template description"
    )
    
    is_system_template: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether this is a system template"
    )
    
    template_data: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        doc="Additional template configuration"
    )
    
    # Indexes
    __table_args__ = (
        Index("idx_notification_templates_type", "type"),
        Index("idx_notification_templates_system", "is_system_template"),
    )
