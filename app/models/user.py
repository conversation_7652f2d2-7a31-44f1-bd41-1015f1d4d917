"""
Simplified User model that matches the existing database schema.

This model only includes the fields that exist in the current database:
- id, email, name, password_hash, is_active, is_verified, created_at, updated_at, last_login_at
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID

from sqlalchemy import String, Boolean, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base


class User(Base):
    """
    Simplified User model matching existing database schema.
    
    This model includes only the essential fields that exist in the database,
    making it compatible with the current schema while maintaining core functionality.
    """
    
    __tablename__ = "users"
    
    # Core authentication fields
    email: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        doc="User's email address (unique identifier)"
    )
    
    password_hash: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Bcrypt hashed password"
    )
    
    # Profile fields
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="User's full name"
    )
    
    # Status fields
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the user account is active"
    )
    
    is_verified: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the user's email is verified"
    )
    
    # Timestamp fields (last_login_at matches database column name)
    last_login_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp of last successful login"
    )
    
    # Relationships (simplified - only essential ones)
    tasks: Mapped[List["Task"]] = relationship(
        "Task",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's tasks"
    )

    gamification: Mapped[Optional["UserGamification"]] = relationship(
        "UserGamification",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False,
        doc="User's gamification profile"
    )

    focus_sessions: Mapped[List["FocusSession"]] = relationship(
        "FocusSession",
        foreign_keys="FocusSession.user_id",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's focus sessions"
    )

    time_blocks: Mapped[List["TimeBlock"]] = relationship(
        "TimeBlock",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's time blocks and calendar entries"
    )

    calendar_integrations: Mapped[List["CalendarIntegration"]] = relationship(
        "CalendarIntegration",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's external calendar integrations"
    )

    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's notifications and reminders"
    )

    notification_preferences: Mapped[Optional["NotificationPreference"]] = relationship(
        "NotificationPreference",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False,
        doc="User's notification preferences"
    )
    
    def __repr__(self) -> str:
        """String representation of the user."""
        return f"<User(email={self.email}, name={self.name}, active={self.is_active})>"
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return self.name
    
    def can_attempt_login(self) -> bool:
        """Check if user can attempt to login."""
        return self.is_active
