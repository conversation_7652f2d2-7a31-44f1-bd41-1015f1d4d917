"""
Time blocking models for Project Chronos.

This module defines models for time blocking, calendar integration, and
ADHD-friendly scheduling features.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import Boolean, <PERSON><PERSON>ey, Integer, String, DateTime, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base


class TimeBlock(Base):
    """
    Time block model for calendar-based time management.
    
    Represents a scheduled block of time that can contain tasks, focus sessions,
    body doubling sessions, or other activities. Designed with ADHD-friendly
    features like buffer time and flexible scheduling.
    """
    
    __tablename__ = "time_blocks"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who owns this time block"
    )
    
    # Block content associations (optional - can be empty time blocks)
    task_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated task (if this is a task block)"
    )
    
    focus_session_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("focus_sessions.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated focus session (if this is a focus block)"
    )
    
    body_doubling_session_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("body_doubling_sessions.id", ondelete="SET NULL"),
        nullable=True,
        doc="Associated body doubling session (if this is a collaboration block)"
    )
    
    # Block details
    title: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Time block title/description"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Detailed description of the time block"
    )
    
    block_type: Mapped[str] = mapped_column(
        String(30),
        nullable=False,
        doc="Type of time block: task, focus, body_doubling, break, buffer, meeting, personal"
    )
    
    # Timing
    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="When the time block starts"
    )
    
    end_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="When the time block ends"
    )
    
    duration_minutes: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Duration of the time block in minutes"
    )
    
    # ADHD-specific features
    buffer_before: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Buffer time before the block in minutes"
    )
    
    buffer_after: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Buffer time after the block in minutes"
    )
    
    energy_level_required: Mapped[str] = mapped_column(
        String(10),
        default="medium",
        nullable=False,
        doc="Required energy level: low, medium, high"
    )
    
    flexibility_level: Mapped[str] = mapped_column(
        String(10),
        default="medium",
        nullable=False,
        doc="How flexible this block is: rigid, medium, flexible"
    )
    
    # Status and completion
    status: Mapped[str] = mapped_column(
        String(20),
        default="scheduled",
        nullable=False,
        doc="Block status: scheduled, active, completed, cancelled, rescheduled"
    )
    
    completion_percentage: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Completion percentage (0-100)"
    )
    
    actual_start_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the block actually started"
    )
    
    actual_end_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the block actually ended"
    )
    
    # Recurrence and patterns
    is_recurring: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this is a recurring time block"
    )
    
    recurrence_pattern: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Recurrence pattern configuration"
    )
    
    parent_block_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("time_blocks.id", ondelete="CASCADE"),
        nullable=True,
        doc="Parent block ID if this is part of a recurring series"
    )
    
    # Calendar integration
    external_calendar_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="External calendar event ID (Google Calendar, Outlook, etc.)"
    )
    
    calendar_provider: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        doc="Calendar provider: google, outlook, apple, etc."
    )
    
    sync_status: Mapped[str] = mapped_column(
        String(20),
        default="local",
        nullable=False,
        doc="Sync status: local, synced, sync_pending, sync_failed"
    )
    
    # ADHD-specific settings
    block_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Block-specific settings and preferences"
    )
    
    # Notifications and reminders
    reminder_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Reminder and notification settings"
    )
    
    # Notes and reflection
    preparation_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Notes for preparing for this time block"
    )
    
    completion_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Notes after completing the time block"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="time_blocks",
        doc="User who owns this time block"
    )
    
    task: Mapped[Optional["Task"]] = relationship(
        "Task",
        back_populates="time_blocks",
        doc="Associated task (if any)"
    )
    
    focus_session: Mapped[Optional["FocusSession"]] = relationship(
        "FocusSession",
        doc="Associated focus session (if any)"
    )
    
    body_doubling_session: Mapped[Optional["BodyDoublingSession"]] = relationship(
        "BodyDoublingSession",
        doc="Associated body doubling session (if any)"
    )
    
    # Recurring series relationships
    parent_block: Mapped[Optional["TimeBlock"]] = relationship(
        "TimeBlock",
        remote_side="TimeBlock.id",
        back_populates="child_blocks",
        doc="Parent block if this is part of a recurring series"
    )
    
    child_blocks: Mapped[List["TimeBlock"]] = relationship(
        "TimeBlock",
        back_populates="parent_block",
        cascade="all, delete-orphan",
        doc="Child blocks if this is a recurring series parent"
    )
    
    def get_total_duration_with_buffers(self) -> int:
        """Get total duration including buffer times."""
        return self.duration_minutes + self.buffer_before + self.buffer_after
    
    def get_actual_duration(self) -> Optional[int]:
        """Get actual duration if block was completed."""
        if not self.actual_start_time or not self.actual_end_time:
            return None
        
        delta = self.actual_end_time - self.actual_start_time
        return int(delta.total_seconds() / 60)
    
    def is_active(self) -> bool:
        """Check if time block is currently active."""
        return self.status == "active"
    
    def is_completed(self) -> bool:
        """Check if time block is completed."""
        return self.status == "completed"
    
    def is_overdue(self) -> bool:
        """Check if time block is overdue."""
        if self.status in ["completed", "cancelled"]:
            return False

        now = datetime.now(timezone.utc)

        # Ensure end_time is timezone-aware
        end_time = self.end_time
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)

        return now > end_time
    
    def get_time_until_start(self) -> Optional[int]:
        """Get minutes until block starts (negative if already started)."""
        now = datetime.now(timezone.utc)

        # Ensure start_time is timezone-aware
        start_time = self.start_time
        if start_time.tzinfo is None:
            start_time = start_time.replace(tzinfo=timezone.utc)

        delta = start_time - now
        return int(delta.total_seconds() / 60)


class CalendarIntegration(Base):
    """
    Calendar integration model for external calendar sync.
    
    Manages connections to external calendar providers like Google Calendar,
    Outlook, Apple Calendar, etc.
    """
    
    __tablename__ = "calendar_integrations"
    
    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who owns this integration"
    )
    
    # Integration details
    provider: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Calendar provider: google, outlook, apple, caldav, etc."
    )
    
    provider_calendar_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Calendar ID from the provider"
    )
    
    calendar_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        doc="Display name for the calendar"
    )
    
    # Authentication and access
    access_token: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Encrypted access token for API access"
    )
    
    refresh_token: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Encrypted refresh token for token renewal"
    )
    
    token_expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the access token expires"
    )
    
    # Sync settings
    sync_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether sync is enabled for this calendar"
    )
    
    sync_direction: Mapped[str] = mapped_column(
        String(20),
        default="bidirectional",
        nullable=False,
        doc="Sync direction: import_only, export_only, bidirectional"
    )
    
    last_sync_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the calendar was last synced"
    )
    
    sync_status: Mapped[str] = mapped_column(
        String(20),
        default="active",
        nullable=False,
        doc="Sync status: active, paused, error, disconnected"
    )
    
    # Sync preferences
    sync_settings: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        default=dict,
        nullable=False,
        doc="Sync preferences and configuration"
    )
    
    # Error tracking
    last_error: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Last sync error message"
    )
    
    error_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of consecutive sync errors"
    )
    
    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        back_populates="calendar_integrations",
        doc="User who owns this calendar integration"
    )
    
    def is_token_expired(self) -> bool:
        """Check if access token is expired."""
        if not self.token_expires_at:
            return False
        
        return datetime.now(timezone.utc) >= self.token_expires_at
    
    def needs_refresh(self) -> bool:
        """Check if token needs refresh (expires within 5 minutes)."""
        if not self.token_expires_at:
            return False
        
        threshold = datetime.now(timezone.utc).timestamp() + 300  # 5 minutes
        return self.token_expires_at.timestamp() <= threshold
