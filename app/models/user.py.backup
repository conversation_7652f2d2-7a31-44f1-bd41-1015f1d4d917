"""
User model with ADHD-specific preferences for Project Chronos.

This module defines the User model with neurodivergent-specific
fields and ADHD-optimized preferences.
"""

from datetime import datetime, timezone
from typing import Optional, List
from sqlalchemy import String, Boolean, JSON, Text, DateTime, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.models.base import Base


class User(Base):
    """
    User model with ADHD-specific preferences and settings.
    
    Stores user authentication data and ADHD-optimized preferences
    for task management, chunking, and adaptive filtering.
    """
    
    __tablename__ = "users"
    
    # Authentication fields
    email: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        index=True,
        doc="User's email address for authentication"
    )
    
    password_hash: Mapped[str] = mapped_column(
        String(255),
        doc="Bcrypt hashed password"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        doc="Whether the user account is active"
    )
    
    is_verified: Mapped[bool] = mapped_column(
        <PERSON><PERSON>an,
        default=False,
        doc="Whether the user's email is verified"
    )
    
    # Profile fields
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="User's full name"
    )

    first_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="User's first name"
    )

    last_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="User's last name"
    )

    display_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        doc="Preferred display name for the interface"
    )

    # Authentication tracking
    last_login: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp of last successful login"
    )

    failed_login_attempts: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of consecutive failed login attempts"
    )

    account_locked_until: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp until which account is locked (if applicable)"
    )

    # Email verification
    email_verification_token: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Token for email verification"
    )

    email_verification_sent_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When email verification was last sent"
    )

    # Password reset
    password_reset_token: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Token for password reset"
    )

    password_reset_sent_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When password reset was last sent"
    )

    # Profile completion tracking
    profile_completion_percentage: Mapped[int] = mapped_column(
        Integer,
        default=20,  # Email and password = 20%
        nullable=False,
        doc="Percentage of profile completion (0-100)"
    )

    onboarding_completed: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether user has completed onboarding process"
    )
    
    # ADHD-specific preferences
    adhd_diagnosis: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        doc="Whether user has ADHD diagnosis (for feature optimization)"
    )
    
    preferred_chunk_size: Mapped[str] = mapped_column(
        String(20),
        default="small",
        doc="Preferred AI chunking size (small/medium/large)"
    )
    
    default_energy_level: Mapped[str] = mapped_column(
        String(20),
        default="medium",
        doc="User's typical energy level (low/medium/high)"
    )
    
    time_blindness_severity: Mapped[str] = mapped_column(
        String(20),
        default="medium",
        doc="Severity of time blindness (low/medium/high)"
    )
    
    task_paralysis_triggers: Mapped[Optional[List[str]]] = mapped_column(
        JSON,
        nullable=True,
        doc="List of task characteristics that trigger paralysis"
    )
    
    preferred_contexts: Mapped[Optional[List[str]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Preferred work contexts (home, office, cafe, etc.)"
    )
    
    focus_session_preferences: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        doc="Preferences for focus sessions and Pomodoro settings"
    )
    
    notification_preferences: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        doc="Notification timing and frequency preferences"
    )
    
    gamification_preferences: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        doc="Gamification and motivation system preferences"
    )
    
    # Adaptive learning data
    task_completion_patterns: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        doc="Learned patterns about when user completes tasks"
    )
    
    energy_level_history: Mapped[Optional[List[dict]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Historical energy level data for pattern recognition"
    )
    
    # Notes and personal insights
    personal_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="User's personal notes about their ADHD patterns"
    )
    
    # Relationships
    tasks: Mapped[List["Task"]] = relationship(
        "Task",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's tasks"
    )

    gamification: Mapped[Optional["UserGamification"]] = relationship(
        "UserGamification",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False,
        doc="User's gamification profile"
    )

    focus_sessions: Mapped[List["FocusSession"]] = relationship(
        "FocusSession",
        foreign_keys="FocusSession.user_id",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's focus sessions"
    )

    time_blocks: Mapped[List["TimeBlock"]] = relationship(
        "TimeBlock",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's time blocks and calendar entries"
    )

    calendar_integrations: Mapped[List["CalendarIntegration"]] = relationship(
        "CalendarIntegration",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's external calendar integrations"
    )

    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="user",
        cascade="all, delete-orphan",
        doc="User's notifications and reminders"
    )

    notification_preferences: Mapped[Optional["NotificationPreference"]] = relationship(
        "NotificationPreference",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False,
        doc="User's notification preferences"
    )
    
    def __repr__(self) -> str:
        """
        String representation of the user.
        
        Returns:
            str: Human-readable representation with email and ADHD status
        """
        return (
            f"<User("
            f"email={self.email}, "
            f"adhd_diagnosis={self.adhd_diagnosis}, "
            f"preferred_chunk_size={self.preferred_chunk_size}"
            f")>"
        )
    
    @property
    def full_name(self) -> str:
        """
        Get user's full name.
        
        Returns:
            str: Combined first and last name, or email if names not set
        """
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        else:
            return self.email.split("@")[0]
    
    def update_energy_pattern(self, energy_level: str, context: dict) -> None:
        """
        Update user's energy level patterns for adaptive learning.

        Args:
            energy_level: Current energy level (low/medium/high)
            context: Context information (time, location, etc.)
        """
        if self.energy_level_history is None:
            self.energy_level_history = []

        self.energy_level_history.append({
            "energy_level": energy_level,
            "timestamp": context.get("timestamp"),
            "context": context
        })

        # Keep only last 100 entries to prevent unbounded growth
        if len(self.energy_level_history) > 100:
            self.energy_level_history = self.energy_level_history[-100:]

    def is_account_locked(self) -> bool:
        """Check if account is currently locked due to failed login attempts.

        Returns:
            True if account is locked, False otherwise
        """
        if self.account_locked_until is None:
            return False
        return datetime.now(timezone.utc) < self.account_locked_until

    def can_attempt_login(self) -> bool:
        """Check if user can attempt to login.

        Returns:
            True if login attempt is allowed, False if account is locked
        """
        return self.is_active and not self.is_account_locked()

    def calculate_profile_completion(self) -> int:
        """Calculate profile completion percentage based on filled fields.

        Returns:
            Profile completion percentage (0-100)

        Note:
            This encourages progressive profile completion without overwhelming
            users during initial registration.
        """
        total_fields = 12  # Total number of optional profile fields
        completed_fields = 2  # Email and password are always completed

        # Check optional fields
        if self.first_name:
            completed_fields += 1
        if self.last_name:
            completed_fields += 1
        if self.display_name:
            completed_fields += 1
        if self.adhd_diagnosis:
            completed_fields += 1
        if self.preferred_chunk_size != "small":  # Non-default value
            completed_fields += 1
        if self.default_energy_level != "medium":  # Non-default value
            completed_fields += 1
        if self.time_blindness_severity != "medium":  # Non-default value
            completed_fields += 1
        if self.preferred_contexts:
            completed_fields += 1
        if self.focus_session_preferences:
            completed_fields += 1
        if self.notification_preferences:
            completed_fields += 1

        return min(100, (completed_fields * 100) // total_fields)
