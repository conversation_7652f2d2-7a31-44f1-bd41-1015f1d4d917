"""Database configuration and session management.

This module provides async database connection management using SQLAlchemy 2.0
with PostgreSQL, optimized for the concurrent nature of ADHD-focused features.
"""

from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import NullPool

from app.core.config import settings


# Create async engine with connection pooling
# Special handling for SQLite in development
if str(settings.DATABASE_URL).startswith("sqlite"):
    engine = create_async_engine(
        str(settings.DATABASE_URL),
        echo=settings.DEBUG,
        future=True,
        connect_args={"check_same_thread": False},
    )
else:
    engine = create_async_engine(
        str(settings.DATABASE_URL),
        echo=settings.DEBUG,  # Log SQL queries in debug mode
        future=True,
        poolclass=NullPool if settings.TESTING else None,  # Disable pooling for tests
        pool_pre_ping=True,  # Verify connections before use
        pool_recycle=3600,   # Recycle connections every hour
    )

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session.
    
    Yields:
        AsyncSession: Database session for dependency injection
        
    Note:
        Automatically handles session cleanup and rollback on errors.
        Designed to be used as a FastAPI dependency.
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """Initialize database tables.
    
    Creates all tables defined in models. Should be called during
    application startup in development/testing environments.
    
    Note:
        In production, use Alembic migrations instead of this function.
    """
    from app.models.base import Base
    
    async with engine.begin() as conn:
        # Import all models to ensure they're registered
        from app.models import (  # noqa: F401
            user, task, gamification, motivation, body_doubling,
            focus, time_blocking, notification
        )

        await conn.run_sync(Base.metadata.create_all)


async def close_db() -> None:
    """Close database connections.
    
    Should be called during application shutdown to properly
    close all database connections.
    """
    await engine.dispose()
