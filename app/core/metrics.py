"""
Prometheus metrics for ADHD-optimized monitoring.

This module defines custom metrics for tracking ADHD-specific user behaviors
and platform performance indicators.
"""

from prometheus_client import Counter, Histogram, Gauge, Summary
from prometheus_fastapi_instrumentator import Instrumentator
from typing import Optional
import time

# API Performance Metrics
api_requests_total = Counter(
    'chronos_api_requests_total',
    'Total number of API requests',
    ['method', 'endpoint', 'status_code']
)

api_request_duration = Histogram(
    'chronos_api_request_duration_seconds',
    'API request duration in seconds',
    ['method', 'endpoint']
)

# User Behavior Metrics
active_users_total = Gauge(
    'chronos_active_users_total',
    'Number of currently active users'
)

user_sessions_total = Counter(
    'chronos_user_sessions_total',
    'Total number of user sessions',
    ['user_type']  # adhd_diagnosed, neurotypical, unknown
)

# Task Management Metrics
tasks_created_total = Counter(
    'chronos_tasks_created_total',
    'Total number of tasks created',
    ['complexity', 'user_energy_level']
)

tasks_completed_total = Counter(
    'chronos_tasks_completed_total',
    'Total number of tasks completed',
    ['complexity', 'completion_time_category']  # on_time, late, early
)

task_completion_rate = Gauge(
    'chronos_task_completion_rate',
    'Current task completion rate as percentage'
)

# ADHD-Specific Metrics
focus_session_duration = Histogram(
    'chronos_focus_session_duration_seconds',
    'Duration of focus sessions in seconds',
    ['session_type', 'interruption_count']  # pomodoro, deep_work, etc.
)

focus_session_duration_avg = Gauge(
    'chronos_focus_session_duration_avg',
    'Average focus session duration in seconds'
)

time_estimation_accuracy = Histogram(
    'chronos_time_estimation_accuracy_ratio',
    'Ratio of actual time to estimated time for tasks',
    ['task_complexity']
)

cognitive_load_score = Histogram(
    'chronos_cognitive_load_score',
    'User-reported cognitive load score (1-10)',
    ['time_of_day', 'task_type']
)

# Dopamine and Motivation Metrics
dopamine_activities_completed = Counter(
    'chronos_dopamine_activities_completed_total',
    'Total dopamine activities completed',
    ['activity_type', 'effectiveness_rating']
)

motivation_level_changes = Counter(
    'chronos_motivation_level_changes_total',
    'Total motivation level changes',
    ['from_level', 'to_level', 'trigger_type']
)

# Notification and Reminder Metrics
notifications_sent_total = Counter(
    'chronos_notifications_sent_total',
    'Total notifications sent',
    ['notification_type', 'delivery_method']
)

notifications_acknowledged_total = Counter(
    'chronos_notifications_acknowledged_total',
    'Total notifications acknowledged',
    ['notification_type', 'response_time_category']  # immediate, delayed, ignored
)

# Body Doubling and Social Features
body_doubling_sessions_total = Counter(
    'chronos_body_doubling_sessions_total',
    'Total body doubling sessions',
    ['session_size', 'duration_category']
)

social_interactions_total = Counter(
    'chronos_social_interactions_total',
    'Total social interactions',
    ['interaction_type']  # message, encouragement, shared_task
)

# Error and Health Metrics
adhd_feature_errors_total = Counter(
    'chronos_adhd_feature_errors_total',
    'Total errors in ADHD-specific features',
    ['feature', 'error_type']
)

user_frustration_indicators = Counter(
    'chronos_user_frustration_indicators_total',
    'Indicators of user frustration',
    ['indicator_type']  # rapid_clicks, session_abandonment, error_repetition
)


class ADHDMetricsCollector:
    """Collector for ADHD-specific metrics."""
    
    def __init__(self):
        self.session_start_times = {}
        self.task_start_times = {}
    
    def record_user_login(self, user_id: str, has_adhd_diagnosis: bool):
        """Record user login event."""
        user_type = "adhd_diagnosed" if has_adhd_diagnosis else "neurotypical"
        user_sessions_total.labels(user_type=user_type).inc()
        self.session_start_times[user_id] = time.time()
    
    def record_task_creation(self, complexity: str, user_energy_level: str):
        """Record task creation with ADHD context."""
        tasks_created_total.labels(
            complexity=complexity,
            user_energy_level=user_energy_level
        ).inc()
    
    def record_task_completion(self, task_id: str, complexity: str, 
                             estimated_duration: float, actual_duration: float):
        """Record task completion with time estimation accuracy."""
        # Determine completion timing
        if actual_duration <= estimated_duration * 1.1:
            completion_category = "on_time"
        elif actual_duration > estimated_duration * 1.5:
            completion_category = "late"
        else:
            completion_category = "early"
        
        tasks_completed_total.labels(
            complexity=complexity,
            completion_time_category=completion_category
        ).inc()
        
        # Record time estimation accuracy
        accuracy_ratio = actual_duration / estimated_duration if estimated_duration > 0 else 1.0
        time_estimation_accuracy.labels(task_complexity=complexity).observe(accuracy_ratio)
    
    def record_focus_session(self, duration: float, session_type: str, 
                           interruption_count: int):
        """Record focus session metrics."""
        focus_session_duration.labels(
            session_type=session_type,
            interruption_count=str(min(interruption_count, 5))  # Cap at 5+ for grouping
        ).observe(duration)
        
        # Update average (simplified - in production, use a sliding window)
        focus_session_duration_avg.set(duration)
    
    def record_cognitive_load(self, score: int, time_of_day: str, task_type: str):
        """Record cognitive load assessment."""
        cognitive_load_score.labels(
            time_of_day=time_of_day,
            task_type=task_type
        ).observe(score)
    
    def record_notification_interaction(self, notification_type: str, 
                                      response_time: float):
        """Record notification interaction patterns."""
        # Categorize response time
        if response_time < 60:  # 1 minute
            response_category = "immediate"
        elif response_time < 3600:  # 1 hour
            response_category = "delayed"
        else:
            response_category = "ignored"
        
        notifications_acknowledged_total.labels(
            notification_type=notification_type,
            response_time_category=response_category
        ).inc()
    
    def record_frustration_indicator(self, indicator_type: str):
        """Record indicators of user frustration."""
        user_frustration_indicators.labels(indicator_type=indicator_type).inc()


# Global metrics collector instance
metrics_collector = ADHDMetricsCollector()


def setup_metrics(app):
    """Set up Prometheus metrics for the FastAPI app."""
    instrumentator = Instrumentator()
    instrumentator.instrument(app)
    instrumentator.expose(app, endpoint="/metrics")
    
    return instrumentator
