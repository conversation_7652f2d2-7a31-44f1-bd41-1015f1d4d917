#!/bin/bash

# Project Chronos + Speechbot AMD Radeon GPU Setup
# Optimized for AMD ROCm acceleration

set -e

echo "🔥 Project Chronos + Speechbot AMD Radeon GPU Setup"
echo "=================================================="
echo "Setting up the world's most advanced ADHD voice assistant with AMD acceleration..."
echo ""

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root for security reasons"
   exit 1
fi

# Function to check command availability
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 is not installed"
        return 1
    else
        echo "✅ $1 is available"
        return 0
    fi
}

# Function to detect AMD GPU
detect_amd_gpu() {
    echo "🔍 Detecting AMD Radeon GPU..."
    
    if lspci | grep -i "amd\|ati\|radeon" > /dev/null; then
        echo "✅ AMD GPU detected:"
        lspci | grep -i "amd\|ati\|radeon"
        return 0
    else
        echo "⚠️ No AMD GPU detected. Continuing with CPU-only mode."
        return 1
    fi
}

# Function to check ROCm installation
check_rocm() {
    echo "🔍 Checking ROCm installation..."
    
    if command -v rocm-smi &> /dev/null; then
        echo "✅ ROCm is installed"
        rocm-smi --showproductname || true
        return 0
    else
        echo "⚠️ ROCm not found. Will provide installation instructions."
        return 1
    fi
}

# Function to check Docker GPU access
check_docker_gpu() {
    echo "🔍 Checking Docker GPU access..."
    
    if [ -e "/dev/kfd" ] && [ -e "/dev/dri" ]; then
        echo "✅ GPU devices available: /dev/kfd, /dev/dri"
        
        # Check permissions
        if groups | grep -q "video\|render"; then
            echo "✅ User is in video/render groups"
        else
            echo "⚠️ User not in video/render groups. Adding to groups..."
            echo "Run: sudo usermod -a -G video,render $USER"
            echo "Then log out and back in."
        fi
        return 0
    else
        echo "❌ GPU devices not accessible"
        return 1
    fi
}

# Main setup
echo "ℹ️ Checking system requirements..."

# Check basic requirements
check_command "docker" || { echo "Please install Docker first"; exit 1; }
check_command "docker-compose" || check_command "docker" || { echo "Please install Docker Compose"; exit 1; }

# Detect GPU
AMD_GPU_AVAILABLE=false
if detect_amd_gpu; then
    AMD_GPU_AVAILABLE=true
fi

# Check ROCm
ROCM_AVAILABLE=false
if check_rocm; then
    ROCM_AVAILABLE=true
fi

# Check Docker GPU access
DOCKER_GPU_READY=false
if check_docker_gpu; then
    DOCKER_GPU_READY=true
fi

echo ""
echo "📊 System Status Summary:"
echo "========================"
echo "AMD GPU Detected: $AMD_GPU_AVAILABLE"
echo "ROCm Installed: $ROCM_AVAILABLE"
echo "Docker GPU Ready: $DOCKER_GPU_READY"
echo ""

# Provide setup instructions if needed
if [ "$AMD_GPU_AVAILABLE" = true ] && [ "$ROCM_AVAILABLE" = false ]; then
    echo "🔧 ROCm Installation Instructions:"
    echo "=================================="
    echo ""
    echo "For Ubuntu 22.04:"
    echo "wget https://repo.radeon.com/amdgpu-install/6.0/ubuntu/jammy/amdgpu-install_6.0.60000-1_all.deb"
    echo "sudo dpkg -i amdgpu-install_6.0.60000-1_all.deb"
    echo "sudo amdgpu-install --usecase=rocm"
    echo ""
    echo "For other distributions, visit: https://rocm.docs.amd.com/en/latest/deploy/linux/quick_start.html"
    echo ""
fi

if [ "$DOCKER_GPU_READY" = false ]; then
    echo "🔧 Docker GPU Setup Instructions:"
    echo "================================="
    echo ""
    echo "1. Add user to groups:"
    echo "   sudo usermod -a -G video,render $USER"
    echo ""
    echo "2. Ensure GPU devices exist:"
    echo "   ls -la /dev/kfd /dev/dri"
    echo ""
    echo "3. Log out and back in for group changes to take effect"
    echo ""
fi

# Set up environment
echo "🔧 Setting up environment..."

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file..."
    cat > .env << EOF
# Project Chronos + Speechbot Configuration
# AMD Radeon GPU Optimized

# Hugging Face Token (required for Dia TTS model)
# Get your token from: https://huggingface.co/settings/tokens
HF_TOKEN=your_hugging_face_token_here

# AMD GPU Configuration
PYTORCH_ROCM_ARCH=gfx1030,gfx1031,gfx1032,gfx1100,gfx1101,gfx1102
HSA_OVERRIDE_GFX_VERSION=10.3.0
DIA_DEVICE=cuda  # PyTorch will auto-detect ROCm

# Database Configuration
POSTGRES_PASSWORD=chronos_secure_pass_2024
DATABASE_URL=postgresql+asyncpg://chronos:chronos_secure_pass_2024@postgres:5432/chronos

# Security
JWT_SECRET_KEY=$(openssl rand -base64 32)

# Performance
WORKERS=4
MAX_CONNECTIONS=100
EOF
    echo "✅ Created .env file with AMD GPU optimizations"
else
    echo "✅ .env file already exists"
fi

# Build and start services
echo ""
echo "🚀 Building and starting Project Chronos + Speechbot..."
echo "======================================================="

# Build with AMD optimizations
echo "Building services with AMD ROCm support..."
if command -v docker-compose &> /dev/null; then
    docker-compose build --no-cache speechbot
    docker-compose up -d
else
    docker compose build --no-cache speechbot
    docker compose up -d
fi

echo ""
echo "⏳ Waiting for services to start..."
sleep 30

# Health check
echo ""
echo "🔍 Checking service health..."
echo "============================="

# Check API
if curl -f -s http://localhost:8090/health -H "Host: api.autism.localhost" > /dev/null; then
    echo "✅ Chronos API: Healthy"
else
    echo "❌ Chronos API: Not responding"
fi

# Check Speechbot
if curl -f -s http://localhost:8090/health -H "Host: speechbot.autism.localhost" > /dev/null; then
    echo "✅ Speechbot: Healthy"
else
    echo "⚠️ Speechbot: Not responding (may still be starting)"
fi

# Check GPU usage in container
echo ""
echo "🔍 Checking GPU access in Speechbot container..."
if command -v docker-compose &> /dev/null; then
    docker-compose exec -T speechbot python3 -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU device: {torch.cuda.get_device_name(0)}')
    print(f'GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')
else:
    print('Running in CPU mode')
" 2>/dev/null || echo "⚠️ Could not check GPU status (container may still be starting)"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "🌐 Access your ADHD productivity platform:"
echo "  • Main API: http://api.autism.localhost:8090"
echo "  • Speechbot: http://speechbot.autism.localhost:8090"
echo "  • Grafana: http://grafana.autism.localhost:8090"
echo "  • MinIO: http://minio.autism.localhost:8090"
echo ""
echo "📚 Next steps:"
echo "  1. Set your Hugging Face token in .env file"
echo "  2. Test voice synthesis: curl -X POST http://speechbot.autism.localhost:8090/synthesize"
echo "  3. Check GPU usage: ./health_check.sh"
echo ""
echo "🔥 Your AMD Radeon GPU is now powering the world's most advanced ADHD voice assistant!"
