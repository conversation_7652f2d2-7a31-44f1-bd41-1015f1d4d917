# PRD: Speechbot Backend API - FastAPI Implementation

**Project:** Chronos Speechbot Backend API  
**Version:** 1.0  
**Date:** June 18, 2025  
**Status:** Draft  
**Parent PRD:** PRD-Speechbot-ADHD-Voice-Assistant.md

## Executive Summary

This PRD details the backend API implementation for Speechbot integration within Project Chronos. It focuses on creating a robust, scalable FastAPI service that provides ADHD-optimized voice synthesis capabilities, real-time adaptation, and seamless integration with the existing Chronos backend infrastructure.

## Problem Statement

The Speechbot backend needs to address several technical challenges:
- **High-Performance TTS**: Real-time voice synthesis with sub-500ms latency
- **ADHD State Processing**: Complex algorithms for cognitive load and energy adaptation
- **Scalability**: Handle multiple concurrent voice synthesis requests
- **Integration**: Seamless integration with existing Chronos microservices
- **Resource Management**: Efficient GPU utilization and model caching

## Solution Overview

The Speechbot backend API will provide:
- FastAPI-based REST and WebSocket endpoints for voice synthesis
- ADHD-aware voice adaptation algorithms
- Efficient Chatterbox model management and caching
- Real-time voice parameter updates via WebSocket
- Comprehensive monitoring and analytics

## API Architecture

### Service Structure

```
speechbot/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI application entry point
│   ├── config.py                  # Configuration management
│   └── dependencies.py            # Dependency injection
├── core/
│   ├── __init__.py
│   ├── tts_engine.py             # Chatterbox integration
│   ├── adhd_adapter.py           # ADHD adaptation algorithms
│   ├── voice_manager.py          # Voice profile management
│   ├── model_cache.py            # Model caching system
│   └── audio_processor.py        # Audio processing utilities
├── api/
│   ├── __init__.py
│   ├── v1/
│   │   ├── __init__.py
│   │   ├── speech.py             # Speech synthesis endpoints
│   │   ├── profiles.py           # Voice profile management
│   │   ├── adaptation.py         # ADHD adaptation endpoints
│   │   └── websocket.py          # Real-time WebSocket handlers
│   └── middleware/
│       ├── __init__.py
│       ├── auth.py               # Authentication middleware
│       ├── rate_limiting.py      # Rate limiting
│       └── monitoring.py         # Request monitoring
├── models/
│   ├── __init__.py
│   ├── speech.py                 # Speech-related Pydantic models
│   ├── voice_profile.py          # Voice profile models
│   ├── adhd_state.py             # ADHD state models
│   └── responses.py              # API response models
├── services/
│   ├── __init__.py
│   ├── synthesis_service.py      # Core synthesis logic
│   ├── adaptation_service.py     # ADHD adaptation service
│   ├── profile_service.py        # Voice profile service
│   └── analytics_service.py      # Usage analytics
├── utils/
│   ├── __init__.py
│   ├── audio_utils.py            # Audio processing utilities
│   ├── adhd_metrics.py           # ADHD-specific calculations
│   ├── cache_utils.py            # Caching utilities
│   └── validation.py             # Input validation
└── tests/
    ├── __init__.py
    ├── test_synthesis.py         # Synthesis testing
    ├── test_adaptation.py        # ADHD adaptation testing
    ├── test_profiles.py          # Profile management testing
    └── test_performance.py       # Performance testing
```

## Core API Endpoints

### 1. Speech Synthesis Endpoints

```python
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from speechbot.models.speech import SpeechSynthesisRequest, SpeechSynthesisResponse
from speechbot.services.synthesis_service import SynthesisService
from speechbot.core.adhd_adapter import ADHDAdapter

router = APIRouter(prefix="/api/v1/speech", tags=["speech"])

@router.post("/synthesize", response_model=SpeechSynthesisResponse)
async def synthesize_speech(
    request: SpeechSynthesisRequest,
    background_tasks: BackgroundTasks,
    synthesis_service: SynthesisService = Depends(),
    current_user: User = Depends(get_current_user)
):
    """
    Synthesize speech with ADHD adaptations.
    
    - **text**: Text to synthesize (max 1000 characters)
    - **voice_profile_id**: Voice profile to use (optional)
    - **adhd_context**: Current ADHD state for adaptation
    - **options**: Additional synthesis options
    """
    try:
        # Validate text length and content
        if len(request.text) > 1000:
            raise HTTPException(status_code=400, detail="Text too long")
        
        # Apply ADHD adaptations
        adapted_config = await synthesis_service.adapt_voice_config(
            request.adhd_context,
            request.voice_profile_id,
            current_user.id
        )
        
        # Synthesize speech
        audio_result = await synthesis_service.synthesize(
            text=request.text,
            voice_config=adapted_config,
            user_id=current_user.id
        )
        
        # Log usage for analytics (background task)
        background_tasks.add_task(
            log_synthesis_usage,
            current_user.id,
            request,
            audio_result
        )
        
        return SpeechSynthesisResponse(
            audio_url=audio_result.url,
            audio_duration=audio_result.duration,
            synthesis_time=audio_result.synthesis_time,
            voice_config=adapted_config,
            cache_hit=audio_result.cache_hit
        )
        
    except Exception as e:
        logger.error(f"Speech synthesis failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Synthesis failed")

@router.post("/synthesize/stream")
async def synthesize_speech_stream(
    request: SpeechSynthesisRequest,
    current_user: User = Depends(get_current_user)
):
    """Stream audio synthesis for real-time playback."""
    
    async def generate_audio_stream():
        try:
            async for audio_chunk in synthesis_service.synthesize_stream(
                text=request.text,
                voice_config=request.voice_config,
                user_id=current_user.id
            ):
                yield audio_chunk
        except Exception as e:
            logger.error(f"Streaming synthesis failed: {str(e)}")
            yield b""  # Empty chunk to signal error
    
    return StreamingResponse(
        generate_audio_stream(),
        media_type="audio/wav",
        headers={"Cache-Control": "no-cache"}
    )

@router.get("/history", response_model=List[SpeechHistoryItem])
async def get_synthesis_history(
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
):
    """Get user's speech synthesis history."""
    
    history = await synthesis_service.get_user_history(
        user_id=current_user.id,
        limit=limit,
        offset=offset
    )
    
    return history
```

### 2. Voice Profile Management

```python
@router.post("/profiles", response_model=VoiceProfileResponse)
async def create_voice_profile(
    profile_data: VoiceProfileCreate,
    audio_files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new voice profile from audio samples.
    
    - **profile_data**: Profile metadata and ADHD preferences
    - **audio_files**: Audio samples for voice cloning (3-10 files)
    """
    try:
        # Validate audio files
        if len(audio_files) < 3 or len(audio_files) > 10:
            raise HTTPException(
                status_code=400, 
                detail="Provide 3-10 audio files"
            )
        
        # Check user profile limit
        existing_profiles = await profile_service.get_user_profiles(current_user.id)
        if len(existing_profiles) >= 5:
            raise HTTPException(
                status_code=400,
                detail="Maximum 5 profiles per user"
            )
        
        # Process audio files
        audio_samples = []
        for file in audio_files:
            if not file.content_type.startswith('audio/'):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid file type: {file.content_type}"
                )
            
            audio_data = await file.read()
            validated_audio = await audio_processor.validate_and_process(audio_data)
            audio_samples.append(validated_audio)
        
        # Create voice profile
        profile = await profile_service.create_profile(
            user_id=current_user.id,
            profile_data=profile_data,
            audio_samples=audio_samples
        )
        
        return VoiceProfileResponse.from_orm(profile)
        
    except Exception as e:
        logger.error(f"Profile creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Profile creation failed")

@router.get("/profiles", response_model=List[VoiceProfileResponse])
async def get_voice_profiles(
    current_user: User = Depends(get_current_user)
):
    """Get user's voice profiles."""
    
    profiles = await profile_service.get_user_profiles(current_user.id)
    return [VoiceProfileResponse.from_orm(p) for p in profiles]

@router.put("/profiles/{profile_id}", response_model=VoiceProfileResponse)
async def update_voice_profile(
    profile_id: UUID,
    update_data: VoiceProfileUpdate,
    current_user: User = Depends(get_current_user)
):
    """Update voice profile settings."""
    
    profile = await profile_service.get_profile(profile_id, current_user.id)
    if not profile:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    updated_profile = await profile_service.update_profile(
        profile_id,
        update_data,
        current_user.id
    )
    
    return VoiceProfileResponse.from_orm(updated_profile)

@router.delete("/profiles/{profile_id}")
async def delete_voice_profile(
    profile_id: UUID,
    current_user: User = Depends(get_current_user)
):
    """Delete a voice profile."""
    
    success = await profile_service.delete_profile(profile_id, current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    return {"message": "Profile deleted successfully"}
```

### 3. ADHD Adaptation Endpoints

```python
@router.post("/adaptation/analyze", response_model=ADHDAdaptationResponse)
async def analyze_adhd_state(
    state_data: ADHDStateInput,
    current_user: User = Depends(get_current_user)
):
    """
    Analyze ADHD state and provide voice adaptation recommendations.
    
    - **state_data**: Current ADHD state metrics
    """
    
    # Analyze ADHD state
    analysis = await adaptation_service.analyze_state(
        state_data,
        current_user.id
    )
    
    # Generate voice adaptations
    voice_adaptations = await adaptation_service.generate_adaptations(
        analysis,
        current_user.voice_preferences
    )
    
    return ADHDAdaptationResponse(
        analysis=analysis,
        voice_adaptations=voice_adaptations,
        confidence_score=analysis.confidence,
        recommendations=analysis.recommendations
    )

@router.post("/adaptation/update", response_model=VoiceConfigResponse)
async def update_voice_adaptation(
    adaptation_request: VoiceAdaptationRequest,
    current_user: User = Depends(get_current_user)
):
    """Update voice configuration based on ADHD state changes."""
    
    # Apply real-time adaptations
    updated_config = await adaptation_service.apply_adaptations(
        adaptation_request.adhd_state,
        adaptation_request.current_config,
        current_user.id
    )
    
    # Cache updated configuration
    await cache_service.set_user_voice_config(
        current_user.id,
        updated_config,
        ttl=300  # 5 minutes
    )
    
    return VoiceConfigResponse(
        voice_config=updated_config,
        adaptation_applied=True,
        cache_duration=300
    )

@router.get("/adaptation/history", response_model=List[AdaptationHistoryItem])
async def get_adaptation_history(
    days: int = 7,
    current_user: User = Depends(get_current_user)
):
    """Get user's voice adaptation history."""
    
    history = await adaptation_service.get_adaptation_history(
        user_id=current_user.id,
        days=days
    )
    
    return history
```

## WebSocket Implementation

### Real-time Voice Adaptation

```python
from fastapi import WebSocket, WebSocketDisconnect
from speechbot.core.websocket_manager import WebSocketManager
from speechbot.models.websocket import WebSocketMessage, MessageType

class SpeechbotWebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_sessions: Dict[str, Dict] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        """Connect user to WebSocket for real-time updates."""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_sessions[user_id] = {
            "connected_at": datetime.utcnow(),
            "last_activity": datetime.utcnow(),
            "voice_config": None
        }

        # Send initial voice configuration
        await self.send_voice_config_update(user_id)

    def disconnect(self, user_id: str):
        """Disconnect user from WebSocket."""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]

    async def send_voice_config_update(self, user_id: str, config: VoiceConfig = None):
        """Send voice configuration update to user."""
        if user_id not in self.active_connections:
            return

        if not config:
            config = await voice_service.get_user_voice_config(user_id)

        message = WebSocketMessage(
            type=MessageType.VOICE_CONFIG_UPDATE,
            data=config.dict(),
            timestamp=datetime.utcnow()
        )

        try:
            await self.active_connections[user_id].send_text(message.json())
        except Exception as e:
            logger.error(f"Failed to send voice config update: {e}")
            self.disconnect(user_id)

    async def handle_adhd_state_update(self, user_id: str, adhd_state: ADHDState):
        """Handle ADHD state update and adapt voice accordingly."""
        try:
            # Analyze new ADHD state
            adaptation = await adaptation_service.analyze_and_adapt(
                adhd_state,
                user_id
            )

            # Send updated voice configuration
            await self.send_voice_config_update(user_id, adaptation.voice_config)

            # Send adaptation feedback
            feedback_message = WebSocketMessage(
                type=MessageType.ADAPTATION_FEEDBACK,
                data={
                    "adaptation_applied": True,
                    "changes": adaptation.changes,
                    "reason": adaptation.reason
                },
                timestamp=datetime.utcnow()
            )

            await self.active_connections[user_id].send_text(feedback_message.json())

        except Exception as e:
            logger.error(f"ADHD state update failed: {e}")
            error_message = WebSocketMessage(
                type=MessageType.ERROR,
                data={"error": "Failed to process ADHD state update"},
                timestamp=datetime.utcnow()
            )
            await self.active_connections[user_id].send_text(error_message.json())

websocket_manager = SpeechbotWebSocketManager()

@router.websocket("/ws/{user_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    user_id: str,
    current_user: User = Depends(get_current_websocket_user)
):
    """WebSocket endpoint for real-time voice adaptation."""

    if current_user.id != user_id:
        await websocket.close(code=1008, reason="Unauthorized")
        return

    await websocket_manager.connect(websocket, user_id)

    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = WebSocketMessage.parse_raw(data)

            # Handle different message types
            if message.type == MessageType.ADHD_STATE_UPDATE:
                adhd_state = ADHDState(**message.data)
                await websocket_manager.handle_adhd_state_update(user_id, adhd_state)

            elif message.type == MessageType.VOICE_CONFIG_REQUEST:
                await websocket_manager.send_voice_config_update(user_id)

            elif message.type == MessageType.PING:
                pong_message = WebSocketMessage(
                    type=MessageType.PONG,
                    data={"timestamp": datetime.utcnow().isoformat()},
                    timestamp=datetime.utcnow()
                )
                await websocket.send_text(pong_message.json())

            # Update last activity
            websocket_manager.user_sessions[user_id]["last_activity"] = datetime.utcnow()

    except WebSocketDisconnect:
        websocket_manager.disconnect(user_id)
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
        websocket_manager.disconnect(user_id)
```

## Core Services Implementation

### 1. Synthesis Service

```python
class SynthesisService:
    def __init__(self):
        self.tts_engine = ChatterboxEngine()
        self.model_cache = ModelCache()
        self.audio_cache = AudioCache()
        self.adhd_adapter = ADHDAdapter()

    async def synthesize(
        self,
        text: str,
        voice_config: VoiceConfig,
        user_id: UUID,
        cache_enabled: bool = True
    ) -> AudioResult:
        """Synthesize speech with caching and optimization."""

        # Check audio cache first
        cache_key = self._generate_cache_key(text, voice_config)
        if cache_enabled:
            cached_audio = await self.audio_cache.get(cache_key)
            if cached_audio:
                return AudioResult(
                    audio_data=cached_audio.data,
                    duration=cached_audio.duration,
                    synthesis_time=0,
                    cache_hit=True
                )

        # Load appropriate model
        model = await self.model_cache.get_model(voice_config.model_id)

        # Synthesize speech
        start_time = time.time()
        audio_data = await self.tts_engine.synthesize(
            text=text,
            model=model,
            config=voice_config
        )
        synthesis_time = time.time() - start_time

        # Process audio
        processed_audio = await self._process_audio(audio_data, voice_config)

        # Cache result
        if cache_enabled:
            await self.audio_cache.set(
                cache_key,
                processed_audio,
                ttl=3600  # 1 hour
            )

        # Log synthesis metrics
        await self._log_synthesis_metrics(
            user_id=user_id,
            text_length=len(text),
            synthesis_time=synthesis_time,
            voice_config=voice_config
        )

        return AudioResult(
            audio_data=processed_audio.data,
            duration=processed_audio.duration,
            synthesis_time=synthesis_time,
            cache_hit=False
        )

    async def synthesize_stream(
        self,
        text: str,
        voice_config: VoiceConfig,
        user_id: UUID
    ) -> AsyncGenerator[bytes, None]:
        """Stream audio synthesis for real-time playback."""

        # Load model
        model = await self.model_cache.get_model(voice_config.model_id)

        # Stream synthesis
        async for audio_chunk in self.tts_engine.synthesize_stream(
            text=text,
            model=model,
            config=voice_config
        ):
            # Process chunk
            processed_chunk = await self._process_audio_chunk(
                audio_chunk,
                voice_config
            )
            yield processed_chunk

    def _generate_cache_key(self, text: str, voice_config: VoiceConfig) -> str:
        """Generate cache key for audio caching."""
        config_hash = hashlib.md5(
            voice_config.json().encode()
        ).hexdigest()[:8]
        text_hash = hashlib.md5(text.encode()).hexdigest()[:8]
        return f"audio:{text_hash}:{config_hash}"
```

### 2. ADHD Adaptation Service

```python
class ADHDAdaptationService:
    def __init__(self):
        self.adaptation_algorithms = {
            'cognitive_load': CognitiveLoadAdapter(),
            'energy_level': EnergyLevelAdapter(),
            'focus_mode': FocusModeAdapter(),
            'emotional_state': EmotionalStateAdapter()
        }
        self.user_preferences_cache = {}

    async def analyze_and_adapt(
        self,
        adhd_state: ADHDState,
        user_id: UUID
    ) -> AdaptationResult:
        """Analyze ADHD state and generate voice adaptations."""

        # Get user preferences
        user_prefs = await self._get_user_preferences(user_id)

        # Analyze current state
        analysis = await self._analyze_adhd_state(adhd_state, user_prefs)

        # Generate adaptations
        adaptations = {}
        for adapter_name, adapter in self.adaptation_algorithms.items():
            if getattr(user_prefs, f"{adapter_name}_enabled", True):
                adaptation = await adapter.adapt(adhd_state, user_prefs)
                adaptations[adapter_name] = adaptation

        # Combine adaptations
        combined_config = await self._combine_adaptations(
            adaptations,
            user_prefs.base_voice_config
        )

        # Calculate confidence score
        confidence = await self._calculate_confidence(analysis, adaptations)

        return AdaptationResult(
            voice_config=combined_config,
            analysis=analysis,
            adaptations=adaptations,
            confidence=confidence,
            changes=self._get_changes_summary(adaptations),
            reason=self._get_adaptation_reason(analysis)
        )

    def _categorize_cognitive_load(self, cognitive_load: float) -> str:
        """Categorize cognitive load level."""
        if cognitive_load >= 0.8:
            return "very_high"
        elif cognitive_load >= 0.6:
            return "high"
        elif cognitive_load >= 0.4:
            return "medium"
        elif cognitive_load >= 0.2:
            return "low"
        else:
            return "very_low"

    def _categorize_energy_level(self, energy_level: int) -> str:
        """Categorize energy level."""
        if energy_level >= 8:
            return "high"
        elif energy_level >= 6:
            return "medium_high"
        elif energy_level >= 4:
            return "medium"
        elif energy_level >= 2:
            return "low"
        else:
            return "very_low"
```

## Data Models

### 1. Pydantic Models

```python
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from enum import Enum

class VoiceConfig(BaseModel):
    """Voice configuration for synthesis."""
    model_id: str = Field(..., description="Voice model identifier")
    pace: float = Field(default=1.0, ge=0.1, le=2.0, description="Speech pace multiplier")
    exaggeration: float = Field(default=0.5, ge=0.25, le=2.0, description="Emotion exaggeration")
    volume: float = Field(default=0.7, ge=0.1, le=1.0, description="Volume level")
    clarity: float = Field(default=0.8, ge=0.1, le=1.0, description="Clarity enhancement")
    temperature: float = Field(default=0.8, ge=0.1, le=1.0, description="Synthesis temperature")
    cfg_weight: float = Field(default=0.5, ge=0.1, le=1.0, description="CFG weight")
    sample_rate: int = Field(default=22050, description="Audio sample rate")

    class Config:
        schema_extra = {
            "example": {
                "model_id": "default_voice",
                "pace": 0.8,
                "exaggeration": 0.6,
                "volume": 0.7,
                "clarity": 0.9,
                "temperature": 0.8,
                "cfg_weight": 0.5,
                "sample_rate": 22050
            }
        }

class ADHDState(BaseModel):
    """ADHD state for voice adaptation."""
    cognitive_load: float = Field(..., ge=0.0, le=1.0, description="Cognitive load level")
    energy_level: int = Field(..., ge=1, le=10, description="Energy level (1-10)")
    focus_mode: str = Field(..., description="Current focus mode")
    emotional_state: str = Field(default="neutral", description="Current emotional state")
    stress_level: float = Field(default=0.5, ge=0.0, le=1.0, description="Stress level")
    attention_span: int = Field(default=25, ge=5, le=120, description="Attention span in minutes")

    @validator('focus_mode')
    def validate_focus_mode(cls, v):
        valid_modes = ['normal', 'deep_work', 'creative', 'administrative', 'break', 'overwhelmed']
        if v not in valid_modes:
            raise ValueError(f'Focus mode must be one of: {valid_modes}')
        return v

    @validator('emotional_state')
    def validate_emotional_state(cls, v):
        valid_states = ['neutral', 'excited', 'calm', 'anxious', 'frustrated', 'motivated']
        if v not in valid_states:
            raise ValueError(f'Emotional state must be one of: {valid_states}')
        return v

class SpeechSynthesisRequest(BaseModel):
    """Request model for speech synthesis."""
    text: str = Field(..., min_length=1, max_length=1000, description="Text to synthesize")
    voice_profile_id: Optional[UUID] = Field(None, description="Voice profile to use")
    adhd_context: ADHDState = Field(..., description="Current ADHD state")
    options: Dict[str, Any] = Field(default_factory=dict, description="Additional options")
    cache_enabled: bool = Field(default=True, description="Enable audio caching")

    @validator('text')
    def validate_text(cls, v):
        # Remove excessive whitespace
        v = ' '.join(v.split())
        if not v.strip():
            raise ValueError('Text cannot be empty')
        return v

class SpeechSynthesisResponse(BaseModel):
    """Response model for speech synthesis."""
    audio_url: str = Field(..., description="URL to synthesized audio")
    audio_duration: float = Field(..., description="Audio duration in seconds")
    synthesis_time: float = Field(..., description="Time taken to synthesize")
    voice_config: VoiceConfig = Field(..., description="Applied voice configuration")
    cache_hit: bool = Field(..., description="Whether result was cached")

class VoiceProfileCreate(BaseModel):
    """Model for creating voice profiles."""
    name: str = Field(..., min_length=1, max_length=100, description="Profile name")
    description: Optional[str] = Field(None, max_length=500, description="Profile description")
    adhd_preferences: Dict[str, Any] = Field(default_factory=dict, description="ADHD-specific preferences")
    is_default: bool = Field(default=False, description="Set as default profile")

    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Name cannot be empty')
        return v.strip()

class VoiceProfileResponse(BaseModel):
    """Response model for voice profiles."""
    id: UUID
    name: str
    description: Optional[str]
    is_default: bool
    adhd_preferences: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    training_status: str

    class Config:
        orm_mode = True

class ADHDAdaptationResponse(BaseModel):
    """Response model for ADHD adaptation analysis."""
    analysis: Dict[str, Any] = Field(..., description="ADHD state analysis")
    voice_adaptations: Dict[str, Any] = Field(..., description="Recommended voice adaptations")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence in adaptations")
    recommendations: List[str] = Field(..., description="Adaptation recommendations")
```

## Performance Optimization

### 1. Caching Strategy

```python
class AudioCache:
    """Redis-based audio caching system."""

    def __init__(self, redis_client):
        self.redis = redis_client
        self.default_ttl = 3600  # 1 hour
        self.max_cache_size = 1000  # Maximum cached items

    async def get(self, cache_key: str) -> Optional[CachedAudio]:
        """Get cached audio data."""
        try:
            cached_data = await self.redis.get(f"audio:{cache_key}")
            if cached_data:
                return CachedAudio.parse_raw(cached_data)
        except Exception as e:
            logger.warning(f"Cache get failed: {e}")
        return None

    async def set(
        self,
        cache_key: str,
        audio_data: ProcessedAudio,
        ttl: int = None
    ) -> bool:
        """Cache audio data."""
        try:
            ttl = ttl or self.default_ttl
            cached_audio = CachedAudio(
                data=audio_data.data,
                duration=audio_data.duration,
                cached_at=datetime.utcnow()
            )

            await self.redis.setex(
                f"audio:{cache_key}",
                ttl,
                cached_audio.json()
            )

            # Update cache statistics
            await self._update_cache_stats(cache_key, len(audio_data.data))
            return True

        except Exception as e:
            logger.error(f"Cache set failed: {e}")
            return False

    async def _update_cache_stats(self, cache_key: str, size: int):
        """Update cache usage statistics."""
        await self.redis.hincrby("cache:stats", "total_items", 1)
        await self.redis.hincrby("cache:stats", "total_size", size)

class ModelCache:
    """In-memory model caching with LRU eviction."""

    def __init__(self, max_models: int = 3):
        self.max_models = max_models
        self.models: OrderedDict[str, ChatterboxModel] = OrderedDict()
        self.model_sizes: Dict[str, int] = {}
        self.lock = asyncio.Lock()

    async def get_model(self, model_id: str) -> ChatterboxModel:
        """Get model with LRU caching."""
        async with self.lock:
            if model_id in self.models:
                # Move to end (most recently used)
                self.models.move_to_end(model_id)
                return self.models[model_id]

            # Load new model
            model = await self._load_model(model_id)

            # Evict oldest model if cache is full
            if len(self.models) >= self.max_models:
                oldest_model_id = next(iter(self.models))
                await self._evict_model(oldest_model_id)

            self.models[model_id] = model
            return model

    async def _load_model(self, model_id: str) -> ChatterboxModel:
        """Load model from disk."""
        model_path = f"/app/models/{model_id}"

        start_time = time.time()
        model = ChatterboxModel.load(model_path)
        load_time = time.time() - start_time

        logger.info(f"Loaded model {model_id} in {load_time:.2f}s")
        return model

    async def _evict_model(self, model_id: str):
        """Evict model from cache."""
        if model_id in self.models:
            del self.models[model_id]
            logger.info(f"Evicted model {model_id} from cache")
```

### 2. Rate Limiting

```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)

class SpeechbotRateLimiter:
    """Custom rate limiter for Speechbot API."""

    def __init__(self):
        self.synthesis_limits = {
            "free": "10/minute",
            "premium": "100/minute",
            "enterprise": "1000/minute"
        }
        self.profile_limits = {
            "free": "1/hour",
            "premium": "5/hour",
            "enterprise": "20/hour"
        }

    def get_user_tier(self, user: User) -> str:
        """Get user's subscription tier."""
        return getattr(user, 'subscription_tier', 'free')

    def synthesis_limit(self, user: User) -> str:
        """Get synthesis rate limit for user."""
        tier = self.get_user_tier(user)
        return self.synthesis_limits.get(tier, self.synthesis_limits["free"])

    def profile_limit(self, user: User) -> str:
        """Get profile creation rate limit for user."""
        tier = self.get_user_tier(user)
        return self.profile_limits.get(tier, self.profile_limits["free"])

rate_limiter = SpeechbotRateLimiter()

# Apply rate limiting to endpoints
@router.post("/synthesize")
@limiter.limit(lambda: rate_limiter.synthesis_limit(get_current_user()))
async def synthesize_speech_with_limit(...):
    # Implementation here
    pass
```

### 3. Monitoring and Metrics

```python
from prometheus_client import Counter, Histogram, Gauge, generate_latest

# Metrics definitions
synthesis_requests_total = Counter(
    'speechbot_synthesis_requests_total',
    'Total synthesis requests',
    ['user_tier', 'voice_profile', 'cache_hit']
)

synthesis_duration_seconds = Histogram(
    'speechbot_synthesis_duration_seconds',
    'Synthesis duration in seconds',
    ['model_id', 'text_length_bucket']
)

active_models_gauge = Gauge(
    'speechbot_active_models',
    'Number of models currently loaded in memory'
)

adhd_adaptations_total = Counter(
    'speechbot_adhd_adaptations_total',
    'Total ADHD adaptations applied',
    ['adaptation_type', 'confidence_level']
)

class MetricsCollector:
    """Collect and export Speechbot metrics."""

    @staticmethod
    def record_synthesis_request(
        user_tier: str,
        voice_profile: str,
        cache_hit: bool,
        duration: float,
        model_id: str,
        text_length: int
    ):
        """Record synthesis request metrics."""
        synthesis_requests_total.labels(
            user_tier=user_tier,
            voice_profile=voice_profile,
            cache_hit=str(cache_hit)
        ).inc()

        # Bucket text length
        if text_length <= 50:
            length_bucket = "short"
        elif text_length <= 200:
            length_bucket = "medium"
        else:
            length_bucket = "long"

        synthesis_duration_seconds.labels(
            model_id=model_id,
            text_length_bucket=length_bucket
        ).observe(duration)

    @staticmethod
    def record_adhd_adaptation(adaptation_type: str, confidence: float):
        """Record ADHD adaptation metrics."""
        confidence_level = "high" if confidence > 0.8 else "medium" if confidence > 0.5 else "low"

        adhd_adaptations_total.labels(
            adaptation_type=adaptation_type,
            confidence_level=confidence_level
        ).inc()

    @staticmethod
    def update_active_models(count: int):
        """Update active models gauge."""
        active_models_gauge.set(count)

@router.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint."""
    return Response(
        generate_latest(),
        media_type="text/plain"
    )
```

## Testing Strategy

### 1. Unit Tests

```python
import pytest
from unittest.mock import AsyncMock, MagicMock
from speechbot.services.synthesis_service import SynthesisService
from speechbot.models.speech import VoiceConfig, ADHDState

@pytest.fixture
async def synthesis_service():
    """Create synthesis service with mocked dependencies."""
    service = SynthesisService()
    service.tts_engine = AsyncMock()
    service.model_cache = AsyncMock()
    service.audio_cache = AsyncMock()
    return service

@pytest.mark.asyncio
async def test_synthesis_with_cache_hit(synthesis_service):
    """Test synthesis with cache hit."""
    # Setup
    text = "Hello, world!"
    voice_config = VoiceConfig(model_id="test_model")
    user_id = UUID("12345678-1234-5678-9012-123456789012")

    # Mock cache hit
    cached_audio = MagicMock()
    cached_audio.data = b"cached_audio_data"
    cached_audio.duration = 2.5
    synthesis_service.audio_cache.get.return_value = cached_audio

    # Execute
    result = await synthesis_service.synthesize(text, voice_config, user_id)

    # Assert
    assert result.cache_hit is True
    assert result.synthesis_time == 0
    assert result.duration == 2.5
    synthesis_service.tts_engine.synthesize.assert_not_called()

@pytest.mark.asyncio
async def test_synthesis_without_cache(synthesis_service):
    """Test synthesis without cache hit."""
    # Setup
    text = "Hello, world!"
    voice_config = VoiceConfig(model_id="test_model")
    user_id = UUID("12345678-1234-5678-9012-123456789012")

    # Mock cache miss
    synthesis_service.audio_cache.get.return_value = None

    # Mock TTS engine
    mock_audio = b"synthesized_audio_data"
    synthesis_service.tts_engine.synthesize.return_value = mock_audio

    # Mock model loading
    mock_model = MagicMock()
    synthesis_service.model_cache.get_model.return_value = mock_model

    # Execute
    result = await synthesis_service.synthesize(text, voice_config, user_id)

    # Assert
    assert result.cache_hit is False
    assert result.synthesis_time > 0
    synthesis_service.tts_engine.synthesize.assert_called_once()
```

### 2. Integration Tests

```python
@pytest.mark.asyncio
async def test_speech_synthesis_endpoint(client, auth_headers):
    """Test speech synthesis API endpoint."""
    request_data = {
        "text": "This is a test message",
        "adhd_context": {
            "cognitive_load": 0.6,
            "energy_level": 7,
            "focus_mode": "normal",
            "emotional_state": "neutral"
        }
    }

    response = await client.post(
        "/api/v1/speech/synthesize",
        json=request_data,
        headers=auth_headers
    )

    assert response.status_code == 200
    data = response.json()
    assert "audio_url" in data
    assert "audio_duration" in data
    assert "synthesis_time" in data
    assert data["voice_config"]["pace"] > 0

@pytest.mark.asyncio
async def test_adhd_adaptation_endpoint(client, auth_headers):
    """Test ADHD adaptation analysis endpoint."""
    request_data = {
        "cognitive_load": 0.8,
        "energy_level": 3,
        "focus_mode": "overwhelmed",
        "emotional_state": "anxious"
    }

    response = await client.post(
        "/api/v1/adaptation/analyze",
        json=request_data,
        headers=auth_headers
    )

    assert response.status_code == 200
    data = response.json()
    assert "analysis" in data
    assert "voice_adaptations" in data
    assert "confidence_score" in data
    assert 0 <= data["confidence_score"] <= 1
```

## Deployment Configuration

### 1. Docker Configuration

```dockerfile
# Dockerfile for Speechbot API
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

# Install Python and dependencies
RUN apt-get update && apt-get install -y \
    python3.9 \
    python3-pip \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 speechbot && chown -R speechbot:speechbot /app
USER speechbot

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["uvicorn", "speechbot.app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. Environment Configuration

```python
# config.py
from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # API Configuration
    api_title: str = "Speechbot API"
    api_version: str = "1.0.0"
    debug: bool = False

    # Database
    database_url: str

    # Redis
    redis_url: str = "redis://localhost:6379"

    # Chatterbox Configuration
    chatterbox_model_path: str = "/app/models/chatterbox"
    chatterbox_device: str = "cuda"
    chatterbox_precision: str = "fp16"

    # Authentication
    jwt_secret_key: str
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 30

    # Rate Limiting
    rate_limit_enabled: bool = True

    # Monitoring
    metrics_enabled: bool = True
    log_level: str = "INFO"

    # ADHD Adaptation
    adaptation_confidence_threshold: float = 0.7
    max_voice_profiles_per_user: int = 5

    class Config:
        env_file = ".env"

settings = Settings()
```

## Error Handling and Validation

### 1. ADHD-Friendly Error Responses

```python
class ADHDFriendlyHTTPException(HTTPException):
    """HTTP exception with ADHD-friendly error messages."""

    def __init__(
        self,
        status_code: int,
        detail: str,
        suggestions: List[str] = None,
        help_url: str = None,
        recovery_steps: List[str] = None
    ):
        super().__init__(status_code=status_code, detail=detail)
        self.suggestions = suggestions or []
        self.help_url = help_url
        self.recovery_steps = recovery_steps or []

@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    """Handle validation errors with ADHD-friendly messages."""

    errors = []
    for error in exc.errors():
        field = error['loc'][-1] if error['loc'] else 'unknown'

        # Generate ADHD-friendly error message
        friendly_message = generate_friendly_validation_message(error)

        errors.append({
            "field": field,
            "message": friendly_message,
            "provided_value": error.get('input'),
            "expected_type": error['type'],
            "suggestions": get_field_suggestions(field, error)
        })

    return JSONResponse(
        status_code=422,
        content={
            "error": {
                "type": "validation_error",
                "message": "Some information needs to be adjusted",
                "details": errors,
                "help_text": "Don't worry! These are easy fixes. Check each field and try again.",
                "help_url": "https://docs.chronos.app/speechbot/troubleshooting"
            }
        }
    )

def generate_friendly_validation_message(error: Dict) -> str:
    """Generate ADHD-friendly validation messages."""

    error_type = error['type']
    field = error['loc'][-1] if error['loc'] else 'field'

    messages = {
        'string_too_short': f"The {field} needs to be a bit longer. Try adding more detail.",
        'string_too_long': f"The {field} is too long. Try keeping it under {error.get('ctx', {}).get('limit_value', 'the limit')} characters.",
        'value_error.missing': f"We need the {field} to continue. Please fill it in.",
        'type_error.integer': f"The {field} should be a number. Try entering just digits.",
        'value_error.email': f"The email format doesn't look right. Try something like '<EMAIL>'.",
        'value_error.url': f"The URL format isn't quite right. Make sure it starts with 'http://' or 'https://'."
    }

    return messages.get(error_type, f"There's an issue with the {field}. Please check the format and try again.")
```

### 2. Rate Limiting with ADHD Considerations

```python
class ADHDRateLimiter:
    """Rate limiter with ADHD-specific considerations."""

    def __init__(self):
        self.redis = Redis.from_url(settings.REDIS_URL)
        self.base_limits = {
            'synthesis': 60,  # per minute
            'profile_creation': 5,  # per hour
            'adaptation_updates': 120  # per minute
        }

    async def check_rate_limit(
        self,
        user_id: str,
        endpoint: str,
        adhd_context: Optional[ADHDState] = None
    ) -> RateLimitResult:
        """Check rate limit with ADHD adaptations."""

        # Get base limit for endpoint
        base_limit = self.base_limits.get(endpoint, 60)

        # Adjust limit based on ADHD state
        adjusted_limit = self._adjust_limit_for_adhd(base_limit, adhd_context)

        # Check current usage
        key = f"rate_limit:{user_id}:{endpoint}"
        current_usage = await self.redis.get(key) or 0
        current_usage = int(current_usage)

        if current_usage >= adjusted_limit:
            # Rate limit exceeded - provide helpful message
            return RateLimitResult(
                allowed=False,
                limit=adjusted_limit,
                current=current_usage,
                reset_time=await self._get_reset_time(key),
                message=self._generate_rate_limit_message(endpoint, adhd_context),
                suggestions=self._get_rate_limit_suggestions(endpoint)
            )

        # Increment usage
        await self.redis.incr(key)
        await self.redis.expire(key, 3600)  # 1 hour expiry

        return RateLimitResult(
            allowed=True,
            limit=adjusted_limit,
            current=current_usage + 1,
            remaining=adjusted_limit - current_usage - 1
        )

    def _adjust_limit_for_adhd(
        self,
        base_limit: int,
        adhd_context: Optional[ADHDState]
    ) -> int:
        """Adjust rate limits based on ADHD state."""

        if not adhd_context:
            return base_limit

        # Increase limits for high cognitive load (user might need more attempts)
        if adhd_context.cognitive_load > 0.7:
            return int(base_limit * 1.5)

        # Increase limits for low energy (user might work slower)
        if adhd_context.energy_level < 4:
            return int(base_limit * 1.3)

        return base_limit

    def _generate_rate_limit_message(
        self,
        endpoint: str,
        adhd_context: Optional[ADHDState]
    ) -> str:
        """Generate ADHD-friendly rate limit messages."""

        messages = {
            'synthesis': "You've been creating lots of voice samples! Take a short break and try again in a few minutes.",
            'profile_creation': "Voice profile creation takes time to do well. Let's wait a bit before creating another one.",
            'adaptation_updates': "Your voice is adapting quickly! Give it a moment to process before making more changes."
        }

        base_message = messages.get(endpoint, "You're working hard! Take a quick break and try again soon.")

        if adhd_context and adhd_context.cognitive_load > 0.7:
            base_message += " This might be a good time for a mental break anyway."

        return base_message
```

### 3. Security Implementation

```python
class SpeechbotSecurity:
    """Security measures for Speechbot API."""

    def __init__(self):
        self.encryption_key = Fernet.generate_key()
        self.fernet = Fernet(self.encryption_key)

    async def encrypt_voice_data(self, audio_data: bytes, user_id: str) -> bytes:
        """Encrypt voice data with user-specific salt."""

        # Add user-specific salt
        salt = hashlib.sha256(f"{user_id}:{datetime.utcnow().isoformat()}".encode()).digest()[:16]
        salted_data = salt + audio_data

        # Encrypt
        encrypted_data = self.fernet.encrypt(salted_data)

        # Log encryption event
        await self._log_security_event(
            user_id=user_id,
            event_type="voice_data_encrypted",
            data_size=len(audio_data)
        )

        return encrypted_data

    async def validate_audio_upload(
        self,
        audio_data: bytes,
        filename: str,
        user_id: str
    ) -> AudioValidationResult:
        """Validate uploaded audio files for security."""

        # Check file size
        if len(audio_data) > 50 * 1024 * 1024:  # 50MB limit
            raise ADHDFriendlyHTTPException(
                status_code=413,
                detail="Audio file is too large",
                suggestions=[
                    "Try recording a shorter sample (under 5 minutes)",
                    "Use a lower quality setting when recording",
                    "Split long recordings into smaller parts"
                ]
            )

        # Check file format
        audio_format = self._detect_audio_format(audio_data)
        allowed_formats = ['wav', 'mp3', 'flac', 'm4a']

        if audio_format not in allowed_formats:
            raise ADHDFriendlyHTTPException(
                status_code=400,
                detail=f"Audio format '{audio_format}' is not supported",
                suggestions=[
                    f"Try converting to one of these formats: {', '.join(allowed_formats)}",
                    "Most recording apps can save in WAV or MP3 format"
                ]
            )

        # Scan for malicious content
        scan_result = await self._scan_audio_content(audio_data)
        if not scan_result.safe:
            await self._log_security_event(
                user_id=user_id,
                event_type="malicious_audio_detected",
                details=scan_result.threats
            )
            raise ADHDFriendlyHTTPException(
                status_code=400,
                detail="Audio file failed security scan",
                suggestions=[
                    "Try recording a new sample",
                    "Make sure your recording device is secure"
                ]
            )

        return AudioValidationResult(
            valid=True,
            format=audio_format,
            duration=scan_result.duration,
            quality_score=scan_result.quality
        )
```

## API Documentation Standards

### 1. OpenAPI Schema Enhancement

```python
# Enhanced OpenAPI configuration
app = FastAPI(
    title="Speechbot API",
    description="""
    # ADHD-Optimized Voice Synthesis API

    The Speechbot API provides voice synthesis capabilities specifically designed for ADHD users.

    ## Key Features
    - **Adaptive Voice Synthesis**: Voice that adapts to your cognitive state
    - **ADHD-Friendly Responses**: Clear, supportive error messages and feedback
    - **Real-time Adaptation**: Voice parameters that change based on your needs
    - **Voice Profile Management**: Create and manage personalized voice profiles

    ## Getting Started
    1. Authenticate using your Chronos account
    2. Create or select a voice profile
    3. Start synthesizing speech with ADHD adaptations

    ## Rate Limits
    Rate limits are adaptive based on your ADHD state:
    - Higher limits when you're experiencing cognitive overload
    - Gentle reminders when you need a break
    - Flexible limits that understand ADHD patterns

    ## Support
    If you need help, our error messages include specific suggestions and recovery steps.
    For additional support, visit our [help center](https://docs.chronos.app/speechbot).
    """,
    version="1.0.0",
    contact={
        "name": "Chronos Support",
        "url": "https://chronos.app/support",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT"
    },
    servers=[
        {
            "url": "https://api.chronos.app",
            "description": "Production server"
        },
        {
            "url": "https://staging-api.chronos.app",
            "description": "Staging server"
        }
    ]
)

# Enhanced endpoint documentation
@router.post(
    "/synthesize",
    response_model=SpeechSynthesisResponse,
    summary="Synthesize speech with ADHD adaptations",
    description="""
    Convert text to speech with automatic ADHD-specific adaptations.

    The voice will automatically adjust based on your current:
    - Cognitive load level
    - Energy state
    - Focus mode
    - Sensory preferences

    **Example Request:**
    ```json
    {
        "text": "Great job completing that task!",
        "adhd_context": {
            "cognitive_load": 0.6,
            "energy_level": 7,
            "focus_mode": "normal"
        }
    }
    ```

    **ADHD Considerations:**
    - Text is automatically chunked for better processing
    - Voice pace adapts to your cognitive load
    - Emotional tone matches your current state
    - Quality feedback is encouraging and specific
    """,
    responses={
        200: {
            "description": "Speech synthesized successfully",
            "content": {
                "application/json": {
                    "example": {
                        "audio_url": "https://cdn.chronos.app/audio/abc123.wav",
                        "audio_duration": 3.2,
                        "synthesis_time": 0.45,
                        "cache_hit": False,
                        "voice_config": {
                            "pace": 0.8,
                            "energy": 0.7,
                            "clarity": 0.9
                        }
                    }
                }
            }
        },
        400: {
            "description": "Invalid request",
            "content": {
                "application/json": {
                    "example": {
                        "error": {
                            "type": "validation_error",
                            "message": "Text is too long for optimal processing",
                            "suggestions": [
                                "Try breaking your text into smaller chunks",
                                "Each chunk should be under 1000 characters"
                            ],
                            "help_url": "https://docs.chronos.app/speechbot/text-limits"
                        }
                    }
                }
            }
        },
        429: {
            "description": "Rate limit exceeded",
            "content": {
                "application/json": {
                    "example": {
                        "error": {
                            "type": "rate_limit",
                            "message": "You've been creating lots of voice samples! Take a short break and try again in a few minutes.",
                            "reset_time": "2025-06-18T15:30:00Z",
                            "suggestions": [
                                "This might be a good time for a mental break",
                                "Try the breathing exercise in your dashboard"
                            ]
                        }
                    }
                }
            }
        }
    }
)
```

## Conclusion

This backend API PRD provides a comprehensive blueprint for implementing a robust, scalable Speechbot service that integrates seamlessly with the Chronos platform. The API is designed with ADHD user needs at its core, providing adaptive voice synthesis, real-time configuration updates, and comprehensive monitoring.

Key highlights include:
- **High-Performance Architecture**: FastAPI with async/await for optimal performance
- **ADHD-Aware Adaptations**: Sophisticated algorithms for voice adaptation based on cognitive state
- **Scalable Design**: Efficient caching, rate limiting, and resource management
- **Comprehensive Monitoring**: Detailed metrics and observability for production deployment
- **Robust Testing**: Unit and integration tests ensuring reliability
- **Security-First Design**: Comprehensive data protection and validation
- **ADHD-Friendly Error Handling**: Supportive, actionable error messages
- **Adaptive Rate Limiting**: Flexible limits that understand ADHD patterns

The implementation follows modern API design principles while prioritizing the unique needs of ADHD users and maintaining high performance standards for real-time voice synthesis.
```
