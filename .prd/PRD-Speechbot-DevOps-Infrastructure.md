# PRD: Speechbot DevOps & Infrastructure

**Project:** Chronos Speechbot Infrastructure & Deployment  
**Version:** 1.0  
**Date:** June 18, 2025  
**Status:** Draft  
**Parent PRD:** PRD-Speechbot-ADHD-Voice-Assistant.md

## Executive Summary

This PRD details the DevOps and infrastructure requirements for deploying and managing the Speechbot service within the Project Chronos ecosystem. It focuses on creating a robust, scalable, and maintainable infrastructure that supports GPU-accelerated voice synthesis while maintaining high availability and performance for ADHD users.

## Problem Statement

The Speechbot infrastructure needs to address several operational challenges:
- **GPU Resource Management**: Efficient allocation and scaling of GPU resources for TTS
- **High Availability**: Ensure voice services are always available for ADHD users
- **Performance Monitoring**: Real-time monitoring of voice synthesis quality and latency
- **Scalability**: Handle varying loads from concurrent voice synthesis requests
- **Security**: Protect voice data and user privacy in production environments

## Solution Overview

The Speechbot infrastructure will provide:
- Docker-based containerization with GPU support
- Kubernetes orchestration for scalability and reliability
- Comprehensive monitoring and alerting systems
- CI/CD pipelines for automated deployment
- Security hardening and compliance measures

## Infrastructure Architecture

### 1. Container Architecture

```yaml
# docker-compose.yml for development
version: '3.8'

services:
  speechbot-api:
    build:
      context: ./speechbot
      dockerfile: Dockerfile.gpu
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
      - DATABASE_URL=************************************/speechbot
      - REDIS_URL=redis://redis:6379
      - CHATTERBOX_MODEL_PATH=/app/models
    volumes:
      - ./models:/app/models:ro
      - ./audio_cache:/app/cache
      - ./logs:/app/logs
    ports:
      - "8001:8000"
    depends_on:
      - postgres
      - redis
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 8G
          cpus: '4'
    networks:
      - chronos-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.speechbot.rule=Host(`speechbot.autism.localhost`)"
      - "traefik.http.services.speechbot.loadbalancer.server.port=8000"
      - "traefik.http.routers.speechbot.middlewares=auth-middleware"

  speechbot-worker:
    build:
      context: ./speechbot
      dockerfile: Dockerfile.worker
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - WORKER_TYPE=synthesis
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./models:/app/models:ro
      - ./audio_cache:/app/cache
    deploy:
      replicas: 2
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - chronos-network

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=speechbot
      - POSTGRES_USER=speechbot_user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - chronos-network

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - chronos-network

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - chronos-network

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - chronos-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  chronos-network:
    external: true
```

### 2. Kubernetes Production Deployment

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: speechbot
  labels:
    name: speechbot
    app.kubernetes.io/name: speechbot
    app.kubernetes.io/part-of: chronos

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: speechbot-config
  namespace: speechbot
data:
  CHATTERBOX_MODEL_PATH: "/app/models"
  CHATTERBOX_DEVICE: "cuda"
  CHATTERBOX_PRECISION: "fp16"
  LOG_LEVEL: "INFO"
  METRICS_ENABLED: "true"
  RATE_LIMIT_ENABLED: "true"
  ADAPTATION_CONFIDENCE_THRESHOLD: "0.7"
  MAX_VOICE_PROFILES_PER_USER: "5"

---
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: speechbot-secrets
  namespace: speechbot
type: Opaque
data:
  DATABASE_URL: <base64-encoded-database-url>
  REDIS_URL: <base64-encoded-redis-url>
  JWT_SECRET_KEY: <base64-encoded-jwt-secret>

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: speechbot-api
  namespace: speechbot
  labels:
    app: speechbot-api
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: speechbot-api
  template:
    metadata:
      labels:
        app: speechbot-api
        version: v1
    spec:
      containers:
      - name: speechbot-api
        image: chronos/speechbot:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: speechbot-secrets
              key: DATABASE_URL
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: speechbot-secrets
              key: REDIS_URL
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: speechbot-secrets
              key: JWT_SECRET_KEY
        envFrom:
        - configMapRef:
            name: speechbot-config
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
            nvidia.com/gpu: 1
          limits:
            memory: "8Gi"
            cpu: "4"
            nvidia.com/gpu: 1
        volumeMounts:
        - name: model-storage
          mountPath: /app/models
          readOnly: true
        - name: cache-storage
          mountPath: /app/cache
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: speechbot-models-pvc
      - name: cache-storage
        persistentVolumeClaim:
          claimName: speechbot-cache-pvc
      nodeSelector:
        accelerator: nvidia-tesla-v100
```

## Monitoring and Observability

### 1. Prometheus Configuration

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "speechbot_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'speechbot-api'
    static_configs:
      - targets: ['speechbot-api:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'speechbot-gpu-metrics'
    static_configs:
      - targets: ['gpu-exporter:9400']
    scrape_interval: 5s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

---
# monitoring/speechbot_rules.yml
groups:
  - name: speechbot.rules
    rules:
      # Synthesis Performance Rules
      - alert: HighSynthesisLatency
        expr: histogram_quantile(0.95, speechbot_synthesis_duration_seconds) > 2.0
        for: 5m
        labels:
          severity: warning
          service: speechbot
        annotations:
          summary: "High synthesis latency detected"
          description: "95th percentile synthesis latency is {{ $value }}s"

      - alert: SynthesisErrorRate
        expr: rate(speechbot_synthesis_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
          service: speechbot
        annotations:
          summary: "High synthesis error rate"
          description: "Synthesis error rate is {{ $value }} errors/sec"

      # GPU Utilization Rules
      - alert: LowGPUUtilization
        expr: nvidia_gpu_utilization_gpu < 20
        for: 10m
        labels:
          severity: info
          service: speechbot
        annotations:
          summary: "Low GPU utilization"
          description: "GPU utilization is {{ $value }}%"

      - alert: GPUMemoryHigh
        expr: nvidia_gpu_memory_used_bytes / nvidia_gpu_memory_total_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: speechbot
        annotations:
          summary: "High GPU memory usage"
          description: "GPU memory usage is {{ $value | humanizePercentage }}"

      # ADHD-Specific Rules
      - alert: AdaptationConfidenceLow
        expr: avg_over_time(speechbot_adaptation_confidence[10m]) < 0.6
        for: 5m
        labels:
          severity: warning
          service: speechbot
        annotations:
          summary: "Low ADHD adaptation confidence"
          description: "Average adaptation confidence is {{ $value }}"

      # Service Health Rules
      - alert: SpeechbotDown
        expr: up{job="speechbot-api"} == 0
        for: 1m
        labels:
          severity: critical
          service: speechbot
        annotations:
          summary: "Speechbot API is down"
          description: "Speechbot API has been down for more than 1 minute"
```

### 2. Grafana Dashboards

```json
{
  "dashboard": {
    "id": null,
    "title": "Speechbot ADHD Voice Assistant",
    "tags": ["speechbot", "adhd", "voice"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Synthesis Requests per Second",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(speechbot_synthesis_requests_total[1m])",
            "legendFormat": "{{user_tier}} - {{cache_hit}}"
          }
        ],
        "yAxes": [
          {
            "label": "Requests/sec",
            "min": 0
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Synthesis Latency Distribution",
        "type": "heatmap",
        "targets": [
          {
            "expr": "rate(speechbot_synthesis_duration_seconds_bucket[5m])",
            "legendFormat": "{{le}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "ADHD Adaptation Metrics",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(speechbot_adhd_adaptations_total[1m])",
            "legendFormat": "{{adaptation_type}} - {{confidence_level}}"
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "GPU Utilization",
        "type": "graph",
        "targets": [
          {
            "expr": "nvidia_gpu_utilization_gpu",
            "legendFormat": "GPU {{gpu}}"
          }
        ],
        "yAxes": [
          {
            "label": "Utilization %",
            "min": 0,
            "max": 100
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
      },
      {
        "id": 5,
        "title": "Voice Profile Usage",
        "type": "piechart",
        "targets": [
          {
            "expr": "sum by (voice_profile) (speechbot_synthesis_requests_total)",
            "legendFormat": "{{voice_profile}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
      },
      {
        "id": 6,
        "title": "Cache Hit Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(speechbot_synthesis_requests_total{cache_hit=\"true\"}[5m])) / sum(rate(speechbot_synthesis_requests_total[5m]))",
            "legendFormat": "Cache Hit Rate"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percentunit",
            "min": 0,
            "max": 1,
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "yellow", "value": 0.5},
                {"color": "green", "value": 0.8}
              ]
            }
          }
        },
        "gridPos": {"h": 4, "w": 6, "x": 0, "y": 24}
      },
      {
        "id": 7,
        "title": "Active Models in Memory",
        "type": "stat",
        "targets": [
          {
            "expr": "speechbot_active_models",
            "legendFormat": "Active Models"
          }
        ],
        "gridPos": {"h": 4, "w": 6, "x": 6, "y": 24}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "10s"
  }
}
```

### 3. Logging Configuration

```yaml
# logging/fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: speechbot
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/speechbot-api-*.log
      pos_file /var/log/fluentd-speechbot.log.pos
      tag kubernetes.speechbot.api
      format json
      time_key time
      time_format %Y-%m-%dT%H:%M:%S.%NZ
    </source>

    <filter kubernetes.speechbot.**>
      @type kubernetes_metadata
      @id filter_kube_metadata
    </filter>

    <filter kubernetes.speechbot.api>
      @type parser
      key_name log
      reserve_data true
      <parse>
        @type json
        time_key timestamp
        time_format %Y-%m-%d %H:%M:%S
      </parse>
    </filter>

    # ADHD-specific log processing
    <filter kubernetes.speechbot.api>
      @type grep
      <regexp>
        key level
        pattern ^(ERROR|WARN|INFO)$
      </regexp>
    </filter>

    # Synthesis performance logs
    <match kubernetes.speechbot.api>
      @type elasticsearch
      host elasticsearch.logging.svc.cluster.local
      port 9200
      index_name speechbot-logs
      type_name _doc
      include_tag_key true
      tag_key @log_name
      <buffer>
        @type file
        path /var/log/fluentd-buffers/speechbot.buffer
        flush_mode interval
        flush_interval 10s
        chunk_limit_size 2M
        queue_limit_length 8
        retry_max_interval 30
        retry_forever true
      </buffer>
    </match>

---
# logging/log-parser.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: speechbot-log-parser
  namespace: speechbot
data:
  parser.py: |
    import json
    import re
    from datetime import datetime

    class SpeechbotLogParser:
        def __init__(self):
            self.synthesis_pattern = re.compile(
                r'Synthesis completed: user=(\w+), duration=(\d+\.?\d*)ms, '
                r'text_length=(\d+), cache_hit=(\w+), model=(\w+)'
            )
            self.adhd_pattern = re.compile(
                r'ADHD adaptation: user=(\w+), cognitive_load=(\d+\.?\d*), '
                r'energy_level=(\d+), confidence=(\d+\.?\d*)'
            )

        def parse_synthesis_log(self, log_line):
            match = self.synthesis_pattern.search(log_line)
            if match:
                return {
                    'event_type': 'synthesis',
                    'user_id': match.group(1),
                    'duration_ms': float(match.group(2)),
                    'text_length': int(match.group(3)),
                    'cache_hit': match.group(4) == 'true',
                    'model_id': match.group(5),
                    'timestamp': datetime.utcnow().isoformat()
                }
            return None

        def parse_adhd_log(self, log_line):
            match = self.adhd_pattern.search(log_line)
            if match:
                return {
                    'event_type': 'adhd_adaptation',
                    'user_id': match.group(1),
                    'cognitive_load': float(match.group(2)),
                    'energy_level': int(match.group(3)),
                    'confidence': float(match.group(4)),
                    'timestamp': datetime.utcnow().isoformat()
                }
            return None
```

## CI/CD Pipeline

### 1. GitHub Actions Workflow

```yaml
# .github/workflows/speechbot-ci-cd.yml
name: Speechbot CI/CD

on:
  push:
    branches: [main, develop]
    paths: ['speechbot/**']
  pull_request:
    branches: [main]
    paths: ['speechbot/**']

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: chronos/speechbot

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_speechbot
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}

    - name: Install dependencies
      run: |
        cd speechbot
        pip install -r requirements.txt
        pip install -r requirements-test.txt

    - name: Run linting
      run: |
        cd speechbot
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        black --check .
        isort --check-only .

    - name: Run type checking
      run: |
        cd speechbot
        mypy .

    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:test_password@localhost:5432/test_speechbot
        REDIS_URL: redis://localhost:6379
        JWT_SECRET_KEY: test_secret_key
      run: |
        cd speechbot
        pytest --cov=. --cov-report=xml --cov-report=html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./speechbot/coverage.xml
        flags: speechbot
        name: speechbot-coverage

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: './speechbot'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  build-and-push:
    needs: [test, security-scan]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./speechbot
        file: ./speechbot/Dockerfile.gpu
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64

  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

    - name: Deploy to staging
      run: |
        cd k8s
        kubectl apply -f namespace.yaml
        kubectl apply -f configmap.yaml
        kubectl apply -f secret.yaml
        envsubst < deployment.yaml | kubectl apply -f -
        kubectl apply -f service.yaml
        kubectl apply -f hpa.yaml

        # Wait for rollout
        kubectl rollout status deployment/speechbot-api -n speechbot --timeout=300s

    - name: Run smoke tests
      run: |
        kubectl run smoke-test --image=curlimages/curl --rm -i --restart=Never -- \
          curl -f http://speechbot-api-service.speechbot.svc.cluster.local/health

  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - uses: actions/checkout@v4

    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG_PRODUCTION }}

    - name: Deploy to production
      run: |
        cd k8s
        kubectl apply -f namespace.yaml
        kubectl apply -f configmap.yaml
        kubectl apply -f secret.yaml

        # Blue-green deployment
        envsubst < deployment.yaml | kubectl apply -f -
        kubectl apply -f service.yaml
        kubectl apply -f hpa.yaml

        # Wait for rollout and verify
        kubectl rollout status deployment/speechbot-api -n speechbot --timeout=600s

        # Health check
        kubectl run health-check --image=curlimages/curl --rm -i --restart=Never -- \
          curl -f http://speechbot-api-service.speechbot.svc.cluster.local/health
```

## Security and Compliance

### 1. Security Hardening

```yaml
# security/network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: speechbot-network-policy
  namespace: speechbot
spec:
  podSelector:
    matchLabels:
      app: speechbot-api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: chronos-frontend
    - namespaceSelector:
        matchLabels:
          name: chronos-api
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: chronos-database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - namespaceSelector:
        matchLabels:
          name: chronos-cache
    ports:
    - protocol: TCP
      port: 6379

---
# security/pod-security-policy.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: speechbot-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'

---
# security/rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: speechbot-service-account
  namespace: speechbot

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: speechbot
  name: speechbot-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: speechbot-role-binding
  namespace: speechbot
subjects:
- kind: ServiceAccount
  name: speechbot-service-account
  namespace: speechbot
roleRef:
  kind: Role
  name: speechbot-role
  apiGroup: rbac.authorization.k8s.io
```

### 2. Data Protection and Privacy

```python
# security/data_protection.py
import hashlib
import hmac
from cryptography.fernet import Fernet
from typing import Optional
import os

class VoiceDataProtection:
    """Handle encryption and protection of voice data."""

    def __init__(self):
        self.encryption_key = os.getenv('VOICE_ENCRYPTION_KEY')
        if not self.encryption_key:
            raise ValueError("VOICE_ENCRYPTION_KEY environment variable required")
        self.fernet = Fernet(self.encryption_key.encode())

    def encrypt_voice_sample(self, audio_data: bytes, user_id: str) -> bytes:
        """Encrypt voice sample with user-specific salt."""
        # Add user-specific salt
        salt = hashlib.sha256(user_id.encode()).digest()[:16]
        salted_data = salt + audio_data

        # Encrypt data
        encrypted_data = self.fernet.encrypt(salted_data)
        return encrypted_data

    def decrypt_voice_sample(self, encrypted_data: bytes, user_id: str) -> bytes:
        """Decrypt voice sample and verify user salt."""
        # Decrypt data
        decrypted_data = self.fernet.decrypt(encrypted_data)

        # Extract and verify salt
        salt = decrypted_data[:16]
        expected_salt = hashlib.sha256(user_id.encode()).digest()[:16]

        if not hmac.compare_digest(salt, expected_salt):
            raise ValueError("Invalid user salt - data may be corrupted")

        return decrypted_data[16:]  # Return audio data without salt

    def generate_voice_watermark(self, audio_data: bytes, user_id: str) -> str:
        """Generate watermark for voice authenticity verification."""
        watermark_data = f"{user_id}:{len(audio_data)}:{hashlib.sha256(audio_data).hexdigest()}"
        return hashlib.sha256(watermark_data.encode()).hexdigest()

    def verify_voice_watermark(
        self,
        audio_data: bytes,
        user_id: str,
        watermark: str
    ) -> bool:
        """Verify voice watermark authenticity."""
        expected_watermark = self.generate_voice_watermark(audio_data, user_id)
        return hmac.compare_digest(watermark, expected_watermark)

# GDPR Compliance utilities
class GDPRCompliance:
    """Handle GDPR compliance for voice data."""

    @staticmethod
    async def anonymize_voice_data(user_id: str) -> bool:
        """Anonymize all voice data for a user."""
        try:
            # Remove voice profiles
            await VoiceProfileService.delete_all_user_profiles(user_id)

            # Remove synthesis history
            await SynthesisHistoryService.delete_user_history(user_id)

            # Remove cached audio
            await AudioCacheService.delete_user_cache(user_id)

            # Log anonymization
            logger.info(f"Voice data anonymized for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to anonymize voice data for user {user_id}: {e}")
            return False

    @staticmethod
    async def export_user_voice_data(user_id: str) -> dict:
        """Export all voice data for a user (GDPR data portability)."""
        export_data = {
            "user_id": user_id,
            "export_timestamp": datetime.utcnow().isoformat(),
            "voice_profiles": [],
            "synthesis_history": [],
            "preferences": {}
        }

        # Export voice profiles (metadata only, not audio)
        profiles = await VoiceProfileService.get_user_profiles(user_id)
        for profile in profiles:
            export_data["voice_profiles"].append({
                "id": str(profile.id),
                "name": profile.name,
                "created_at": profile.created_at.isoformat(),
                "adhd_preferences": profile.adhd_preferences
            })

        # Export synthesis history (last 30 days)
        history = await SynthesisHistoryService.get_user_history(
            user_id,
            days=30
        )
        for item in history:
            export_data["synthesis_history"].append({
                "timestamp": item.created_at.isoformat(),
                "text_length": len(item.text_content),
                "voice_config": item.voice_config,
                "adhd_context": item.adhd_context
            })

        # Export preferences
        preferences = await UserPreferencesService.get_preferences(user_id)
        if preferences:
            export_data["preferences"] = preferences.dict()

        return export_data
```

## Disaster Recovery and Backup

### 1. Backup Strategy

```bash
#!/bin/bash
# scripts/backup-speechbot.sh

set -e

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/speechbot"
S3_BUCKET="chronos-speechbot-backups"

echo "Starting Speechbot backup at $BACKUP_DATE"

# Create backup directory
mkdir -p "$BACKUP_DIR/$BACKUP_DATE"

# Backup PostgreSQL database
echo "Backing up PostgreSQL database..."
kubectl exec -n speechbot deployment/postgres -- pg_dump \
  -U speechbot_user speechbot > "$BACKUP_DIR/$BACKUP_DATE/database.sql"

# Backup Redis data
echo "Backing up Redis data..."
kubectl exec -n speechbot deployment/redis -- redis-cli BGSAVE
kubectl cp speechbot/redis-pod:/data/dump.rdb "$BACKUP_DIR/$BACKUP_DATE/redis-dump.rdb"

# Backup voice models (if not already in S3)
echo "Backing up voice models..."
kubectl cp speechbot/speechbot-api-pod:/app/models "$BACKUP_DIR/$BACKUP_DATE/models"

# Backup Kubernetes configurations
echo "Backing up Kubernetes configurations..."
kubectl get all -n speechbot -o yaml > "$BACKUP_DIR/$BACKUP_DATE/k8s-resources.yaml"
kubectl get configmaps -n speechbot -o yaml > "$BACKUP_DIR/$BACKUP_DATE/configmaps.yaml"
kubectl get secrets -n speechbot -o yaml > "$BACKUP_DIR/$BACKUP_DATE/secrets.yaml"

# Compress backup
echo "Compressing backup..."
tar -czf "$BACKUP_DIR/speechbot-backup-$BACKUP_DATE.tar.gz" \
  -C "$BACKUP_DIR" "$BACKUP_DATE"

# Upload to S3
echo "Uploading to S3..."
aws s3 cp "$BACKUP_DIR/speechbot-backup-$BACKUP_DATE.tar.gz" \
  "s3://$S3_BUCKET/daily/$BACKUP_DATE/"

# Cleanup local backup (keep last 7 days)
find "$BACKUP_DIR" -name "speechbot-backup-*.tar.gz" -mtime +7 -delete

echo "Backup completed successfully: speechbot-backup-$BACKUP_DATE.tar.gz"

# Send notification
curl -X POST "$SLACK_WEBHOOK_URL" \
  -H 'Content-type: application/json' \
  --data "{\"text\":\"✅ Speechbot backup completed: $BACKUP_DATE\"}"
```

### 2. Disaster Recovery Plan

```yaml
# disaster-recovery/recovery-plan.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: disaster-recovery-plan
  namespace: speechbot
data:
  recovery-steps.md: |
    # Speechbot Disaster Recovery Plan

    ## Recovery Time Objectives (RTO)
    - **Critical Services**: 15 minutes
    - **Voice Synthesis**: 30 minutes
    - **Voice Profiles**: 1 hour
    - **Historical Data**: 4 hours

    ## Recovery Point Objectives (RPO)
    - **Database**: 1 hour (hourly backups)
    - **Voice Models**: 24 hours (daily sync)
    - **Cache Data**: Acceptable loss (can be rebuilt)

    ## Recovery Procedures

    ### 1. Database Recovery
    ```bash
    # Restore from latest backup
    kubectl create job --from=cronjob/postgres-backup postgres-restore
    kubectl wait --for=condition=complete job/postgres-restore --timeout=300s
    ```

    ### 2. Voice Model Recovery
    ```bash
    # Restore models from S3
    kubectl create job --from=cronjob/model-sync model-restore
    kubectl wait --for=condition=complete job/model-restore --timeout=600s
    ```

    ### 3. Service Recovery
    ```bash
    # Redeploy services
    kubectl apply -f k8s/
    kubectl rollout status deployment/speechbot-api --timeout=300s
    ```

    ### 4. Verification Steps
    - [ ] Health check endpoints responding
    - [ ] Voice synthesis working
    - [ ] ADHD adaptations functioning
    - [ ] Monitoring and alerts active
    - [ ] Performance within SLA

  runbook.sh: |
    #!/bin/bash
    # Automated disaster recovery runbook

    set -e

    echo "🚨 Starting Speechbot disaster recovery..."

    # Check if cluster is accessible
    if ! kubectl cluster-info &> /dev/null; then
      echo "❌ Kubernetes cluster not accessible"
      exit 1
    fi

    # Restore database
    echo "📊 Restoring database..."
    kubectl create job --from=cronjob/postgres-backup postgres-restore-$(date +%s)

    # Restore voice models
    echo "🎤 Restoring voice models..."
    kubectl create job --from=cronjob/model-sync model-restore-$(date +%s)

    # Deploy services
    echo "🚀 Deploying services..."
    kubectl apply -f /recovery/k8s/

    # Wait for services to be ready
    echo "⏳ Waiting for services..."
    kubectl wait --for=condition=available deployment/speechbot-api --timeout=300s

    # Run health checks
    echo "🔍 Running health checks..."
    kubectl run health-check --image=curlimages/curl --rm -i --restart=Never -- \
      curl -f http://speechbot-api-service.speechbot.svc.cluster.local/health

    echo "✅ Disaster recovery completed successfully"
```

## Performance Optimization

### 1. Resource Optimization

```yaml
# optimization/resource-limits.yaml
apiVersion: v1
kind: LimitRange
metadata:
  name: speechbot-limits
  namespace: speechbot
spec:
  limits:
  - default:
      cpu: "2"
      memory: "4Gi"
      nvidia.com/gpu: "1"
    defaultRequest:
      cpu: "1"
      memory: "2Gi"
      nvidia.com/gpu: "1"
    type: Container
  - max:
      cpu: "8"
      memory: "16Gi"
      nvidia.com/gpu: "2"
    min:
      cpu: "0.5"
      memory: "1Gi"
    type: Container

---
# optimization/vertical-pod-autoscaler.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: speechbot-api-vpa
  namespace: speechbot
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: speechbot-api
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: speechbot-api
      maxAllowed:
        cpu: "4"
        memory: "8Gi"
      minAllowed:
        cpu: "1"
        memory: "2Gi"
      controlledResources: ["cpu", "memory"]
```

### 2. Performance Tuning

```python
# optimization/performance_tuner.py
import asyncio
import psutil
import GPUtil
from typing import Dict, Any

class SpeechbotPerformanceTuner:
    """Automatically tune Speechbot performance based on system metrics."""

    def __init__(self):
        self.cpu_threshold = 80  # CPU usage percentage
        self.memory_threshold = 85  # Memory usage percentage
        self.gpu_threshold = 90  # GPU usage percentage
        self.optimization_interval = 60  # seconds

    async def monitor_and_optimize(self):
        """Continuously monitor and optimize performance."""
        while True:
            try:
                metrics = await self._collect_metrics()
                optimizations = await self._analyze_metrics(metrics)

                if optimizations:
                    await self._apply_optimizations(optimizations)

                await asyncio.sleep(self.optimization_interval)

            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(self.optimization_interval)

    async def _collect_metrics(self) -> Dict[str, Any]:
        """Collect system performance metrics."""
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()

        # Memory metrics
        memory = psutil.virtual_memory()
        memory_percent = memory.percent

        # GPU metrics
        gpus = GPUtil.getGPUs()
        gpu_metrics = []
        for gpu in gpus:
            gpu_metrics.append({
                'id': gpu.id,
                'load': gpu.load * 100,
                'memory_used': gpu.memoryUsed,
                'memory_total': gpu.memoryTotal,
                'memory_percent': (gpu.memoryUsed / gpu.memoryTotal) * 100,
                'temperature': gpu.temperature
            })

        return {
            'cpu_percent': cpu_percent,
            'cpu_count': cpu_count,
            'memory_percent': memory_percent,
            'memory_available': memory.available,
            'gpu_metrics': gpu_metrics,
            'timestamp': datetime.utcnow()
        }

    async def _analyze_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze metrics and determine optimizations."""
        optimizations = {}

        # CPU optimization
        if metrics['cpu_percent'] > self.cpu_threshold:
            optimizations['reduce_worker_threads'] = True
            optimizations['enable_cpu_affinity'] = True

        # Memory optimization
        if metrics['memory_percent'] > self.memory_threshold:
            optimizations['reduce_model_cache_size'] = True
            optimizations['enable_memory_compression'] = True

        # GPU optimization
        for gpu in metrics['gpu_metrics']:
            if gpu['memory_percent'] > self.gpu_threshold:
                optimizations['reduce_batch_size'] = True
                optimizations['enable_model_quantization'] = True

            if gpu['temperature'] > 80:  # Celsius
                optimizations['reduce_gpu_frequency'] = True

        return optimizations

    async def _apply_optimizations(self, optimizations: Dict[str, Any]):
        """Apply performance optimizations."""
        for optimization, enabled in optimizations.items():
            if not enabled:
                continue

            try:
                if optimization == 'reduce_worker_threads':
                    await self._reduce_worker_threads()
                elif optimization == 'reduce_model_cache_size':
                    await self._reduce_model_cache_size()
                elif optimization == 'reduce_batch_size':
                    await self._reduce_batch_size()
                elif optimization == 'enable_model_quantization':
                    await self._enable_model_quantization()

                logger.info(f"Applied optimization: {optimization}")

            except Exception as e:
                logger.error(f"Failed to apply optimization {optimization}: {e}")
```

## Conclusion

This DevOps and Infrastructure PRD provides a comprehensive blueprint for deploying, monitoring, and maintaining the Speechbot service in production. The infrastructure is designed to handle the unique requirements of GPU-accelerated voice synthesis while ensuring high availability, security, and performance for ADHD users.

Key highlights include:
- **GPU-Optimized Infrastructure**: Kubernetes deployment with NVIDIA GPU support
- **Comprehensive Monitoring**: Prometheus, Grafana, and custom ADHD-specific metrics
- **Robust CI/CD**: Automated testing, security scanning, and deployment pipelines
- **Security Hardening**: Network policies, RBAC, and data protection measures
- **Disaster Recovery**: Automated backup and recovery procedures
- **Performance Optimization**: Auto-scaling and performance tuning capabilities

The implementation follows cloud-native best practices while addressing the specific operational needs of an ADHD-focused voice synthesis service.
```
      tolerations:
      - key: nvidia.com/gpu
        operator: Exists
        effect: NoSchedule

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: speechbot-api-service
  namespace: speechbot
  labels:
    app: speechbot-api
spec:
  selector:
    app: speechbot-api
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  type: ClusterIP

---
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: speechbot-api-hpa
  namespace: speechbot
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: speechbot-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: synthesis_requests_per_second
      target:
        type: AverageValue
        averageValue: "10"

---
# k8s/pvc.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: speechbot-models-pvc
  namespace: speechbot
spec:
  accessModes:
    - ReadOnlyMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: speechbot-cache-pvc
  namespace: speechbot
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
```

### 3. GPU Node Configuration

```yaml
# k8s/gpu-node-pool.yaml
apiVersion: v1
kind: Node
metadata:
  name: gpu-node-1
  labels:
    accelerator: nvidia-tesla-v100
    node-type: gpu-worker
    speechbot.chronos.dev/gpu-enabled: "true"
spec:
  # Node configuration for GPU support
  taints:
  - key: nvidia.com/gpu
    value: "true"
    effect: NoSchedule
  
---
# NVIDIA Device Plugin DaemonSet
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: nvidia-device-plugin-daemonset
  namespace: kube-system
spec:
  selector:
    matchLabels:
      name: nvidia-device-plugin-ds
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        name: nvidia-device-plugin-ds
    spec:
      tolerations:
      - key: nvidia.com/gpu
        operator: Exists
        effect: NoSchedule
      priorityClassName: "system-node-critical"
      containers:
      - image: nvcr.io/nvidia/k8s-device-plugin:v0.14.0
        name: nvidia-device-plugin-ctr
        args: ["--fail-on-init-error=false"]
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop: ["ALL"]
        volumeMounts:
        - name: device-plugin
          mountPath: /var/lib/kubelet/device-plugins
      volumes:
      - name: device-plugin
        hostPath:
          path: /var/lib/kubelet/device-plugins
      nodeSelector:
        accelerator: nvidia-tesla-v100
```
