# Chronos Docker Development Environment

This document describes how to set up and run the complete Chronos ADHD task management system using Docker.

## 🏗️ Architecture Overview

The Docker environment includes:

### Core Services
- **chronos-api**: FastAPI backend with ADHD-optimized features
- **chronos-ui**: Next.js frontend with ADHD-responsive design
- **postgres**: PostgreSQL database with ADHD-specific schema
- **redis**: Redis cache for session management and real-time features

### Infrastructure Services
- **traefik**: Reverse proxy with automatic service discovery
- **prometheus**: Metrics collection and monitoring
- **grafana**: Dashboards and visualization
- **mailhog**: Email testing (catches all outgoing emails)
- **minio**: S3-compatible storage for file uploads

## 🚀 Quick Start

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ available RAM
- 10GB+ available disk space

### 1. Start the Environment
```bash
# Make scripts executable (first time only)
chmod +x scripts/*.sh

# Start all services
./scripts/start-dev.sh
```

The script will:
- Create necessary directories and configuration files
- Add local domain entries to `/etc/hosts`
- Pull and build Docker images
- Start all services
- Wait for health checks
- Display access URLs

### 2. Access the Application
Once started, you can access:

**Main Application:**
- 🌐 **Chronos UI**: http://chronos.autism.localhost
- 🔌 **API**: http://api.autism.localhost
- 📚 **API Docs**: http://localhost:8000/docs

**Development Tools:**
- 🚦 **Traefik Dashboard**: http://traefik.autism.localhost
- 📊 **Grafana**: http://grafana.autism.localhost (admin/admin)
- 📈 **Prometheus**: http://prometheus.autism.localhost
- 📧 **Mailhog**: http://mail.autism.localhost
- 🗄️ **MinIO Console**: http://minio.autism.localhost (chronos/chronos123)

**Demo Account:**
- Email: `<EMAIL>`
- Password: `demo123`

### 3. Stop the Environment
```bash
# Stop services (keep data)
./scripts/stop-dev.sh

# Stop and remove all data
./scripts/stop-dev.sh --clean-data

# Stop and remove images
./scripts/stop-dev.sh --remove-images
```

## 🔧 Development Workflow

### Viewing Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f chronos-api
docker-compose logs -f chronos-ui

# Last 100 lines
docker-compose logs --tail=100 chronos-api
```

### Restarting Services
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart chronos-api

# Rebuild and restart
docker-compose up --build -d chronos-api
```

### Database Access
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U chronos -d chronos

# Connect to Redis
docker-compose exec redis redis-cli

# View database logs
docker-compose logs postgres
```

### File Changes and Hot Reload

**Frontend (chronos-ui):**
- Files are mounted as volumes
- Next.js hot reload works automatically
- Changes appear immediately in browser

**Backend (chronos-api):**
- Files are mounted as volumes
- FastAPI auto-reload is enabled
- API restarts automatically on code changes

## 🐛 Troubleshooting

### Common Issues

**1. Port Conflicts**
```bash
# Check what's using a port
lsof -i :8000
lsof -i :3000

# Stop conflicting services
sudo systemctl stop nginx  # if using nginx
sudo systemctl stop apache2  # if using apache
```

**2. Permission Issues**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

**3. Domain Resolution Issues**
```bash
# Check /etc/hosts entries
grep autism.localhost /etc/hosts

# Manually add if missing
sudo bash -c 'echo "127.0.0.1 chronos.autism.localhost" >> /etc/hosts'
```

**4. Database Connection Issues**
```bash
# Check database health
docker-compose exec postgres pg_isready -U chronos

# Reset database
docker-compose down -v
docker-compose up -d postgres
```

**5. Out of Disk Space**
```bash
# Clean up Docker
docker system prune -a
docker volume prune

# Remove unused images
docker image prune -a
```

### Health Checks
```bash
# Check service status
docker-compose ps

# Check specific service health
docker-compose exec chronos-api curl -f http://localhost:8000/health

# Check all endpoints
curl -f http://localhost:8000/health
curl -f http://localhost:3000/api/health
```

### Performance Issues
```bash
# Check resource usage
docker stats

# Limit memory usage (in docker-compose.yml)
services:
  chronos-api:
    deploy:
      resources:
        limits:
          memory: 1G
```

## 📊 Monitoring and Metrics

### Grafana Dashboards
Access Grafana at http://grafana.autism.localhost
- Default login: admin/admin
- Pre-configured Prometheus datasource
- Custom dashboards for ADHD metrics

### Prometheus Metrics
Access Prometheus at http://prometheus.autism.localhost
- API response times
- Database connection pools
- ADHD state changes
- User engagement metrics

### Application Logs
```bash
# Structured JSON logs
docker-compose logs chronos-api | jq '.'

# Filter by log level
docker-compose logs chronos-api | grep ERROR

# Follow logs in real-time
docker-compose logs -f --tail=50 chronos-api
```

## 🔒 Security Considerations

### Development vs Production
This Docker setup is optimized for development:
- Debug mode enabled
- Insecure defaults (admin/admin passwords)
- All services exposed on localhost
- No SSL/TLS encryption

### Production Deployment
For production, you'll need:
- Secure passwords and secrets
- SSL/TLS certificates
- Firewall configuration
- Resource limits
- Health monitoring
- Backup strategies

## 🧪 Testing

### Running Tests
```bash
# Backend tests
docker-compose exec chronos-api poetry run pytest

# Frontend tests
docker-compose exec chronos-ui npm test

# Integration tests
docker-compose exec chronos-api poetry run pytest tests/integration/
```

### Test Database
```bash
# Create test database
docker-compose exec postgres createdb -U chronos chronos_test

# Run tests with test database
docker-compose exec chronos-api DATABASE_URL=postgresql+asyncpg://chronos:chronos_dev@postgres:5432/chronos_test poetry run pytest
```

## 📁 Directory Structure

```
.
├── docker-compose.yml          # Main Docker Compose configuration
├── .env.example               # Environment variables template
├── scripts/
│   ├── start-dev.sh          # Start development environment
│   └── stop-dev.sh           # Stop development environment
├── database/
│   └── init.sql              # Database initialization script
├── monitoring/
│   ├── prometheus.yml        # Prometheus configuration
│   └── grafana/              # Grafana dashboards and config
├── chronos-ui/               # Next.js frontend application
│   ├── Dockerfile           # Frontend Docker image
│   └── ...                  # Frontend source code
└── ...                      # Backend source code (FastAPI)
```

## 🤝 Contributing

When working with the Docker environment:

1. **Make changes to source code** - files are mounted as volumes
2. **Test changes** - services auto-reload on file changes
3. **Check logs** - use `docker-compose logs` to debug issues
4. **Run tests** - use the test commands above
5. **Commit changes** - Docker configuration is part of the repository

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. View service logs: `docker-compose logs [service-name]`
3. Check service health: `docker-compose ps`
4. Restart services: `docker-compose restart`
5. Clean restart: `./scripts/stop-dev.sh --clean-data && ./scripts/start-dev.sh`

---

**Happy coding!** 🚀 The ADHD-optimized Chronos system is ready for development.
