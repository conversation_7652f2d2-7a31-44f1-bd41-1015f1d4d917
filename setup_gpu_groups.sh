#!/bin/bash

# AMD Radeon GPU Groups Setup for Project Chronos
# Run this script to configure GPU access for Docker containers

echo "🔥 AMD Radeon RX 7900 XT/XTX GPU Groups Setup"
echo "=============================================="
echo ""

# Check current user
USER_NAME=$(whoami)
echo "Current user: $USER_NAME"
echo ""

# Check current groups
echo "Current groups:"
groups
echo ""

# Check if user is already in required groups
if groups | grep -q "video"; then
    echo "✅ User is already in 'video' group"
    VIDEO_OK=true
else
    echo "❌ User is NOT in 'video' group"
    VIDEO_OK=false
fi

if groups | grep -q "render"; then
    echo "✅ User is already in 'render' group"
    RENDER_OK=true
else
    echo "❌ User is NOT in 'render' group"
    RENDER_OK=false
fi

echo ""

# If groups are missing, provide instructions
if [ "$VIDEO_OK" = false ] || [ "$RENDER_OK" = false ]; then
    echo "🔧 GPU Groups Setup Required"
    echo "============================"
    echo ""
    echo "Please run these commands to add your user to GPU groups:"
    echo ""
    
    if [ "$VIDEO_OK" = false ]; then
        echo "sudo usermod -a -G video $USER_NAME"
    fi
    
    if [ "$RENDER_OK" = false ]; then
        echo "sudo usermod -a -G render $USER_NAME"
    fi
    
    echo ""
    echo "After running the commands above:"
    echo "1. Log out of your session completely"
    echo "2. Log back in"
    echo "3. Run this script again to verify"
    echo ""
    echo "Alternative: Restart your computer to ensure group changes take effect"
    echo ""
    exit 1
else
    echo "✅ All GPU groups are configured correctly!"
fi

# Check GPU device permissions
echo ""
echo "🔍 Checking GPU Device Permissions"
echo "=================================="

if [ -c "/dev/kfd" ]; then
    echo "✅ /dev/kfd exists"
    ls -la /dev/kfd
else
    echo "❌ /dev/kfd not found"
fi

if [ -d "/dev/dri" ]; then
    echo "✅ /dev/dri exists"
    ls -la /dev/dri/
else
    echo "❌ /dev/dri not found"
fi

echo ""
echo "🔍 Testing GPU Access"
echo "===================="

# Test if we can access GPU devices
if [ -r "/dev/kfd" ] && [ -w "/dev/kfd" ]; then
    echo "✅ /dev/kfd is readable and writable"
    KFD_OK=true
else
    echo "⚠️ /dev/kfd access limited (this is normal, Docker will handle it)"
    KFD_OK=false
fi

if [ -r "/dev/dri/renderD128" ]; then
    echo "✅ /dev/dri/renderD128 is readable"
    DRI_OK=true
else
    echo "⚠️ /dev/dri/renderD128 access limited (this is normal, Docker will handle it)"
    DRI_OK=false
fi

echo ""
echo "🐳 Docker GPU Configuration"
echo "==========================="

# Check if Docker daemon configuration exists
if [ -f "/etc/docker/daemon.json" ]; then
    echo "✅ Docker daemon.json exists"
    echo "Current configuration:"
    cat /etc/docker/daemon.json
else
    echo "⚠️ Docker daemon.json not found"
    echo ""
    echo "To enable ROCm support in Docker, create /etc/docker/daemon.json:"
    echo ""
    cat << 'EOF'
{
  "runtimes": {
    "rocm": {
      "path": "/usr/bin/rocm-runtime",
      "runtimeArgs": []
    }
  }
}
EOF
    echo ""
    echo "Commands to set this up:"
    echo "sudo tee /etc/docker/daemon.json << 'EOF'"
    echo "{"
    echo '  "runtimes": {'
    echo '    "rocm": {'
    echo '      "path": "/usr/bin/rocm-runtime",'
    echo '      "runtimeArgs": []'
    echo '    }'
    echo '  }'
    echo "}"
    echo "EOF"
    echo ""
    echo "sudo systemctl restart docker"
fi

echo ""
echo "🧪 GPU Test Command"
echo "=================="
echo ""
echo "Once groups and Docker are configured, test GPU access with:"
echo ""
echo "docker run --rm \\"
echo "  --device=/dev/kfd \\"
echo "  --device=/dev/dri \\"
echo "  --group-add video \\"
echo "  --security-opt seccomp:unconfined \\"
echo "  -e PYTORCH_ROCM_ARCH=gfx1100,gfx1101,gfx1102 \\"
echo "  -e HSA_OVERRIDE_GFX_VERSION=11.0.0 \\"
echo "  chronos-speechbot-amd \\"
echo "  python3 -c \""
echo "import torch"
echo "print(f'PyTorch: {torch.__version__}')"
echo "print(f'CUDA available: {torch.cuda.is_available()}')"
echo "if torch.cuda.is_available():"
echo "    print(f'GPU: {torch.cuda.get_device_name(0)}')"
echo "    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')"
echo "\""

echo ""
echo "🎯 Summary"
echo "=========="
if [ "$VIDEO_OK" = true ] && [ "$RENDER_OK" = true ]; then
    echo "✅ GPU groups: Configured"
else
    echo "❌ GPU groups: Need configuration"
fi

echo "✅ ROCm: Installed and working"
echo "✅ GPU devices: Available"
echo "⚠️ Docker ROCm: Needs configuration"

echo ""
echo "Next steps:"
echo "1. Configure GPU groups (if needed)"
echo "2. Configure Docker ROCm runtime"
echo "3. Test GPU access in container"
echo "4. Launch Speechbot with AMD acceleration"
