# 🚀 Project Chronos + Speechbot Production Environment
# The World's Most Advanced ADHD-Optimized Voice Assistant Platform

# ================================================================
# 🗄️ DATABASE CONFIGURATION
# ================================================================

# PostgreSQL Database URL for async connections
DATABASE_URL=postgresql+asyncpg://chronos:CHANGE_PASSWORD@postgres:5432/chronos

# PostgreSQL Database Configuration
POSTGRES_DB=chronos
POSTGRES_USER=chronos
POSTGRES_PASSWORD=CHANGE_THIS_PASSWORD_IN_PRODUCTION

# ================================================================
# 🔐 SECURITY CONFIGURATION
# ================================================================

# JWT Secret Key (GENERATE NEW FOR PRODUCTION!)
# Generate with: openssl rand -base64 32
JWT_SECRET_KEY=GENERATE_NEW_JWT_SECRET_KEY_FOR_PRODUCTION

# Application Secret Key (GENERATE NEW FOR PRODUCTION!)
# Generate with: openssl rand -base64 32
SECRET_KEY=GENERATE_NEW_SECRET_KEY_FOR_PRODUCTION

# JWT Token Expiration
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# ================================================================
# 🌐 NETWORK CONFIGURATION
# ================================================================

# Domain Configuration (UPDATE FOR YOUR DOMAIN)
DOMAIN=your-domain.com
API_DOMAIN=api.your-domain.com
SPEECHBOT_DOMAIN=speechbot.your-domain.com

# CORS Origins (UPDATE FOR YOUR DOMAINS)
CORS_ORIGINS=https://your-domain.com,https://api.your-domain.com

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Project Chronos

# ================================================================
# 🎭 SPEECHBOT CONFIGURATION
# ================================================================

# Speechbot Service URL
SPEECHBOT_URL=http://speechbot:8001

# ADHD Mode Configuration
DEFAULT_ADHD_MODE=calm
ENABLE_EMOTION_DETECTION=true
NONVERBAL_FREQUENCY=0.1

# Voice Profile Settings
MAX_VOICE_PROFILES_PER_USER=10
VOICE_SAMPLE_MIN_DURATION=5
VOICE_SAMPLE_MAX_DURATION=30

# ================================================================
# 📦 REDIS CONFIGURATION
# ================================================================

# Redis Connection
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=CHANGE_REDIS_PASSWORD_FOR_PRODUCTION

# ================================================================
# 🤖 AI/ML CONFIGURATION
# ================================================================

# Hugging Face Configuration
HF_TOKEN=your_huggingface_token_here
HF_CACHE_DIR=/app/models

# GPU Configuration
CUDA_VISIBLE_DEVICES=0
DIA_PRECISION=fp16
DIA_BATCH_SIZE=4

# ================================================================
# 📧 EMAIL CONFIGURATION
# ================================================================

# SMTP Settings
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-app-password
SMTP_TLS=true

# Email Settings
FROM_EMAIL=<EMAIL>
FROM_NAME=Project Chronos

# ================================================================
# 📊 MONITORING & LOGGING
# ================================================================

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Metrics and Monitoring
ENABLE_METRICS=true
PROMETHEUS_MULTIPROC_DIR=/tmp/prometheus_multiproc

# ================================================================
# 🗄️ STORAGE CONFIGURATION
# ================================================================

# MinIO Object Storage
MINIO_ROOT_USER=CHANGE_MINIO_USER
MINIO_ROOT_PASSWORD=CHANGE_MINIO_PASSWORD_STRONG
MINIO_ENDPOINT=minio:9000
MINIO_SECURE=false

# Storage Buckets
VOICE_SAMPLES_BUCKET=voice-samples
GENERATED_AUDIO_BUCKET=generated-audio

# ================================================================
# 🚀 PRODUCTION SETTINGS
# ================================================================

# Environment
ENVIRONMENT=production
DEBUG=false
RELOAD=false
WORKERS=4

# SSL/TLS Configuration
FORCE_HTTPS=true
SSL_CERT_PATH=/etc/ssl/certs/chronos.crt
SSL_KEY_PATH=/etc/ssl/private/chronos.key

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# ================================================================
# 📈 ANALYTICS & PRIVACY
# ================================================================

# Analytics Configuration
ENABLE_ANALYTICS=true
ANALYTICS_RETENTION_DAYS=90

# User Privacy
ANONYMIZE_USER_DATA=true
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=365

# ================================================================
# 🎯 ADHD-SPECIFIC FEATURES
# ================================================================

# Body Doubling Configuration
DEFAULT_SESSION_DURATION=25
MAX_SESSION_DURATION=180
ENCOURAGEMENT_FREQUENCIES=low,medium,high

# Accessibility Features
ENABLE_HIGH_CONTRAST=true
ENABLE_REDUCED_MOTION=true
ENABLE_FOCUS_INDICATORS=true

# ================================================================
# 🔌 BACKUP & MAINTENANCE
# ================================================================

# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Maintenance Windows
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System maintenance in progress

# ================================================================
# 📝 PRODUCTION CHECKLIST
# ================================================================

# BEFORE DEPLOYING TO PRODUCTION:
# [ ] Change all default passwords and secrets
# [ ] Generate strong JWT and secret keys
# [ ] Configure proper domain names
# [ ] Set up SSL/TLS certificates
# [ ] Configure email settings
# [ ] Set up monitoring and alerting
# [ ] Configure backup strategy
# [ ] Test all functionality
# [ ] Review security settings
# [ ] Update CORS origins
# [ ] Configure rate limiting
# [ ] Set up log aggregation
# [ ] Configure firewall rules
# [ ] Test disaster recovery
# [ ] Document deployment process
