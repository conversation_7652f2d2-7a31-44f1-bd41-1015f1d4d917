import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  Zap,
  Brain,
  Waves,
  Activity,
  Clock,
  Mic
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface RealTimeStreamingProps {
  userId: string;
  userPreferences: any;
}

interface EmotionScores {
  [key: string]: number;
}

interface StreamingChunk {
  audio: ArrayBuffer;
  metadata: any;
  isComplete: boolean;
}

export const RealTimeStreaming: React.FC<RealTimeStreamingProps> = ({
  userId,
  userPreferences
}) => {
  const [text, setText] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [autoEmotionDetection, setAutoEmotionDetection] = useState(true);
  const [emotionScores, setEmotionScores] = useState<EmotionScores>({});
  const [suggestedMode, setSuggestedMode] = useState('calm');
  const [streamingProgress, setStreamingProgress] = useState(0);
  const [chunksReceived, setChunksReceived] = useState(0);
  const [totalDuration, setTotalDuration] = useState(0);
  const [currentStreamId, setCurrentStreamId] = useState<string | null>(null);
  
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioBufferRef = useRef<AudioBuffer[]>([]);
  const sourceNodesRef = useRef<AudioBufferSourceNode[]>([]);
  const streamReaderRef = useRef<ReadableStreamDefaultReader | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Initialize Web Audio API
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }

    return () => {
      // Cleanup
      stopStreaming();
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  const analyzeEmotion = useCallback(async () => {
    if (!text.trim() || !autoEmotionDetection) return;

    try {
      const response = await fetch('/api/v1/speechbot/streaming/emotion/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          user_id: userId
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setEmotionScores(result.emotion_scores);
        setSuggestedMode(result.suggested_adhd_mode);
      }
    } catch (error) {
      console.error('Emotion analysis failed:', error);
    }
  }, [text, userId, autoEmotionDetection]);

  useEffect(() => {
    // Debounced emotion analysis
    const timer = setTimeout(analyzeEmotion, 1000);
    return () => clearTimeout(timer);
  }, [text, analyzeEmotion]);

  const startStreaming = async () => {
    if (!text.trim()) {
      toast({
        title: "No Text",
        description: "Please enter some text to synthesize.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsStreaming(true);
      setChunksReceived(0);
      setTotalDuration(0);
      setStreamingProgress(0);
      audioBufferRef.current = [];

      // Resume audio context if suspended
      if (audioContextRef.current?.state === 'suspended') {
        await audioContextRef.current.resume();
      }

      const streamId = `stream_${Date.now()}`;
      setCurrentStreamId(streamId);

      // Start streaming synthesis
      const response = await fetch('/api/v1/speechbot/streaming/synthesize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          user_id: userId,
          adhd_mode: suggestedMode,
          auto_emotion_detection: autoEmotionDetection,
          stream_id: streamId,
          include_nonverbals: userPreferences?.enable_nonverbals ?? true
        }),
      });

      if (!response.ok) {
        throw new Error('Streaming failed');
      }

      // Set up server-sent events for progress
      eventSourceRef.current = new EventSource(
        `/api/v1/speechbot/streaming/events/${streamId}`
      );

      eventSourceRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (event.type === 'stream_progress') {
          setChunksReceived(data.chunks_sent);
          setTotalDuration(data.total_duration);
          setStreamingProgress(Math.min((data.elapsed_time / 10) * 100, 95)); // Estimate
        } else if (event.type === 'stream_complete') {
          setStreamingProgress(100);
          eventSourceRef.current?.close();
        }
      };

      // Process streaming response
      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      streamReaderRef.current = reader;
      await processStreamingChunks(reader);

      toast({
        title: "Streaming Complete",
        description: `Generated ${totalDuration.toFixed(1)}s of audio in real-time`,
      });

    } catch (error) {
      console.error('Streaming failed:', error);
      toast({
        title: "Streaming Failed",
        description: "Failed to start real-time streaming. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsStreaming(false);
      setCurrentStreamId(null);
    }
  };

  const processStreamingChunks = async (reader: ReadableStreamDefaultReader) => {
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        
        // Process complete chunks
        const chunks = buffer.split('\r\n--chunk-');
        buffer = chunks.pop() || ''; // Keep incomplete chunk

        for (const chunk of chunks) {
          if (chunk.includes('Content-Type: audio/wav')) {
            await processAudioChunk(chunk);
          }
        }
      }
    } catch (error) {
      console.error('Stream processing failed:', error);
    }
  };

  const processAudioChunk = async (chunkData: string) => {
    try {
      // Extract audio data from chunk
      const audioStart = chunkData.indexOf('\r\n\r\n') + 4;
      const audioEnd = chunkData.lastIndexOf('\r\n');
      const audioData = chunkData.slice(audioStart, audioEnd);
      
      // Convert to ArrayBuffer (this is simplified - real implementation would need proper parsing)
      const audioBuffer = new ArrayBuffer(audioData.length);
      const view = new Uint8Array(audioBuffer);
      for (let i = 0; i < audioData.length; i++) {
        view[i] = audioData.charCodeAt(i);
      }

      // Decode and play audio
      if (audioContextRef.current) {
        const decodedBuffer = await audioContextRef.current.decodeAudioData(audioBuffer);
        audioBufferRef.current.push(decodedBuffer);
        
        if (!isPaused) {
          playAudioBuffer(decodedBuffer);
        }
      }

      setChunksReceived(prev => prev + 1);

    } catch (error) {
      console.error('Audio chunk processing failed:', error);
    }
  };

  const playAudioBuffer = (buffer: AudioBuffer) => {
    if (!audioContextRef.current) return;

    const source = audioContextRef.current.createBufferSource();
    source.buffer = buffer;
    source.connect(audioContextRef.current.destination);
    source.start();
    
    sourceNodesRef.current.push(source);
  };

  const pauseStreaming = () => {
    setIsPaused(!isPaused);
    
    if (isPaused) {
      // Resume - play any buffered audio
      audioBufferRef.current.forEach(buffer => playAudioBuffer(buffer));
      audioBufferRef.current = [];
    } else {
      // Pause - stop current audio
      sourceNodesRef.current.forEach(source => {
        try {
          source.stop();
        } catch (e) {
          // Source might already be stopped
        }
      });
      sourceNodesRef.current = [];
    }
  };

  const stopStreaming = () => {
    setIsStreaming(false);
    setIsPaused(false);
    
    // Stop all audio
    sourceNodesRef.current.forEach(source => {
      try {
        source.stop();
      } catch (e) {
        // Source might already be stopped
      }
    });
    sourceNodesRef.current = [];
    audioBufferRef.current = [];

    // Stop stream reader
    if (streamReaderRef.current) {
      streamReaderRef.current.cancel();
      streamReaderRef.current = null;
    }

    // Close event source
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    // Stop stream on server
    if (currentStreamId) {
      fetch(`/api/v1/speechbot/streaming/streams/${currentStreamId}`, {
        method: 'DELETE'
      }).catch(console.error);
    }

    setCurrentStreamId(null);
    setStreamingProgress(0);
    setChunksReceived(0);
    setTotalDuration(0);
  };

  const getEmotionColor = (emotion: string, score: number) => {
    const intensity = Math.min(score * 2, 1); // Double the intensity for visibility
    
    const colors = {
      calm: `rgba(59, 130, 246, ${intensity})`,      // Blue
      excited: `rgba(245, 158, 11, ${intensity})`,   // Yellow
      focused: `rgba(34, 197, 94, ${intensity})`,    // Green
      overwhelmed: `rgba(168, 85, 247, ${intensity})`, // Purple
      motivated: `rgba(249, 115, 22, ${intensity})`   // Orange
    };
    
    return colors[emotion as keyof typeof colors] || `rgba(107, 114, 128, ${intensity})`;
  };

  return (
    <div className="space-y-6">
      {/* Real-Time Streaming Card */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Waves className="h-5 w-5 text-blue-600" />
            <span>Real-Time Streaming Synthesis</span>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              BETA
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Text Input */}
          <div className="space-y-2">
            <Label htmlFor="streaming-text">Text for Real-Time Synthesis</Label>
            <Textarea
              id="streaming-text"
              placeholder="Enter text for real-time voice generation..."
              value={text}
              onChange={(e) => setText(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>{text.length} characters</span>
              <span>Real-time generation as you type</span>
            </div>
          </div>

          {/* Emotion Detection */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="auto-emotion"
                checked={autoEmotionDetection}
                onCheckedChange={setAutoEmotionDetection}
              />
              <Label htmlFor="auto-emotion">
                Auto-detect emotion and adjust ADHD mode
              </Label>
            </div>

            {autoEmotionDetection && Object.keys(emotionScores).length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Brain className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium">Detected Emotions:</span>
                  <Badge className="bg-purple-100 text-purple-800">
                    Suggested: {suggestedMode}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-5 gap-2">
                  {Object.entries(emotionScores).map(([emotion, score]) => (
                    <div
                      key={emotion}
                      className="p-2 rounded-lg text-center text-xs"
                      style={{ backgroundColor: getEmotionColor(emotion, score) }}
                    >
                      <div className="font-medium capitalize">{emotion}</div>
                      <div>{(score * 100).toFixed(0)}%</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Streaming Controls */}
          <div className="space-y-4">
            {!isStreaming ? (
              <Button
                onClick={startStreaming}
                disabled={!text.trim()}
                className="w-full"
                size="lg"
              >
                <Waves className="h-4 w-4 mr-2" />
                Start Real-Time Streaming
              </Button>
            ) : (
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <Button
                    onClick={pauseStreaming}
                    variant="outline"
                    size="lg"
                  >
                    {isPaused ? (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Resume
                      </>
                    ) : (
                      <>
                        <Pause className="h-4 w-4 mr-2" />
                        Pause
                      </>
                    )}
                  </Button>
                  
                  <Button
                    onClick={stopStreaming}
                    variant="destructive"
                    size="lg"
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Stop
                  </Button>
                </div>

                {/* Streaming Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Streaming Progress</span>
                    <span>{Math.round(streamingProgress)}%</span>
                  </div>
                  <Progress value={streamingProgress} className="w-full" />
                  
                  <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Activity className="h-4 w-4" />
                      <span>{chunksReceived} chunks</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{totalDuration.toFixed(1)}s audio</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Volume2 className="h-4 w-4" />
                      <span>{isPaused ? 'Paused' : 'Playing'}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Features Info */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50">
        <CardContent className="p-4">
          <h3 className="font-semibold text-green-900 mb-2">🚀 Real-Time Streaming Features</h3>
          <ul className="text-sm text-green-800 space-y-1">
            <li>• <strong>Instant Playback:</strong> Audio starts playing as it's generated</li>
            <li>• <strong>Emotion Detection:</strong> Automatically adjusts ADHD mode based on text content</li>
            <li>• <strong>Chunk-based Processing:</strong> Smooth, continuous audio without waiting</li>
            <li>• <strong>Real-time Controls:</strong> Pause, resume, and stop during generation</li>
            <li>• <strong>Progress Tracking:</strong> Visual feedback on generation progress</li>
            <li>• <strong>ADHD Optimized:</strong> Reduces waiting time and cognitive load</li>
          </ul>
        </CardContent>
      </Card>

      {/* Browser Compatibility */}
      <Alert>
        <Mic className="h-4 w-4" />
        <AlertDescription>
          <strong>Browser Requirements:</strong> Real-time streaming requires a modern browser with 
          Web Audio API support. For best results, use Chrome, Firefox, or Safari.
        </AlertDescription>
      </Alert>
    </div>
  );
};
