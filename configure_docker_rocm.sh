#!/bin/bash

# Docker ROCm Configuration for AMD Radeon RX 7900 XT/XTX
# Configure Docker to support AMD GPU acceleration

echo "🐳 Docker ROCm Configuration for AMD Radeon RX 7900 XT/XTX"
echo "=========================================================="
echo ""

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root for security reasons"
   echo "Run it as your regular user, it will prompt for sudo when needed"
   exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    exit 1
else
    echo "✅ Docker is installed"
    docker --version
fi

echo ""

# Check current Docker daemon configuration
echo "🔍 Checking current Docker configuration..."
if [ -f "/etc/docker/daemon.json" ]; then
    echo "✅ Docker daemon.json exists:"
    cat /etc/docker/daemon.json
    echo ""
    
    # Check if ROCm runtime is already configured
    if grep -q "rocm" /etc/docker/daemon.json; then
        echo "✅ ROCm runtime appears to be configured"
        ROCM_CONFIGURED=true
    else
        echo "⚠️ ROCm runtime not found in configuration"
        ROCM_CONFIGURED=false
    fi
else
    echo "⚠️ Docker daemon.json not found"
    ROCM_CONFIGURED=false
fi

echo ""

# Check if ROCm runtime is available
echo "🔍 Checking ROCm runtime availability..."
if command -v rocm-runtime &> /dev/null; then
    echo "✅ rocm-runtime is available"
    ROCM_RUNTIME_AVAILABLE=true
elif [ -f "/usr/bin/rocm-runtime" ]; then
    echo "✅ rocm-runtime found at /usr/bin/rocm-runtime"
    ROCM_RUNTIME_AVAILABLE=true
else
    echo "⚠️ rocm-runtime not found"
    ROCM_RUNTIME_AVAILABLE=false
fi

echo ""

# Configure Docker daemon if needed
if [ "$ROCM_CONFIGURED" = false ]; then
    echo "🔧 Configuring Docker for ROCm support..."
    echo ""
    
    # Backup existing daemon.json if it exists
    if [ -f "/etc/docker/daemon.json" ]; then
        echo "📋 Backing up existing daemon.json..."
        sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup
        echo "✅ Backup created at /etc/docker/daemon.json.backup"
    fi
    
    # Create or update daemon.json
    echo "📝 Creating Docker daemon configuration..."
    
    if [ -f "/etc/docker/daemon.json" ]; then
        # Merge with existing configuration
        echo "⚠️ Existing daemon.json found. Manual merge may be required."
        echo "Current configuration:"
        cat /etc/docker/daemon.json
        echo ""
        echo "Recommended ROCm configuration to add:"
        cat << 'EOF'
{
  "runtimes": {
    "rocm": {
      "path": "/usr/bin/rocm-runtime",
      "runtimeArgs": []
    }
  }
}
EOF
    else
        # Create new daemon.json
        echo "Creating new daemon.json with ROCm support..."
        sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "runtimes": {
    "rocm": {
      "path": "/usr/bin/rocm-runtime",
      "runtimeArgs": []
    }
  },
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF
        echo "✅ Docker daemon.json created with ROCm support"
    fi
    
    echo ""
    echo "🔄 Restarting Docker daemon..."
    sudo systemctl restart docker
    
    # Wait for Docker to restart
    echo "⏳ Waiting for Docker to restart..."
    sleep 5
    
    # Verify Docker is running
    if sudo systemctl is-active --quiet docker; then
        echo "✅ Docker restarted successfully"
    else
        echo "❌ Docker failed to restart"
        echo "Check logs with: sudo journalctl -u docker.service"
        exit 1
    fi
else
    echo "✅ Docker ROCm configuration already present"
fi

echo ""

# Test Docker GPU access
echo "🧪 Testing Docker GPU Access"
echo "============================"

# Test basic Docker functionality
echo "Testing basic Docker functionality..."
if docker run --rm hello-world > /dev/null 2>&1; then
    echo "✅ Docker basic functionality works"
else
    echo "❌ Docker basic functionality failed"
    exit 1
fi

# Test GPU device access
echo "Testing GPU device access..."
if docker run --rm --device=/dev/kfd --device=/dev/dri ubuntu:22.04 ls -la /dev/kfd /dev/dri > /dev/null 2>&1; then
    echo "✅ GPU devices accessible in container"
else
    echo "⚠️ GPU devices may not be accessible (this might be normal)"
fi

echo ""

# Provide test commands
echo "🚀 GPU Test Commands"
echo "==================="
echo ""
echo "Test 1: Basic GPU device access"
echo "docker run --rm --device=/dev/kfd --device=/dev/dri ubuntu:22.04 ls -la /dev/kfd /dev/dri"
echo ""

echo "Test 2: ROCm in container (if you have a ROCm image)"
echo "docker run --rm --device=/dev/kfd --device=/dev/dri rocm/pytorch:latest rocm-smi"
echo ""

echo "Test 3: PyTorch ROCm with your Speechbot image"
echo "docker run --rm \\"
echo "  --device=/dev/kfd \\"
echo "  --device=/dev/dri \\"
echo "  --group-add video \\"
echo "  --security-opt seccomp:unconfined \\"
echo "  -e PYTORCH_ROCM_ARCH=gfx1100,gfx1101,gfx1102 \\"
echo "  -e HSA_OVERRIDE_GFX_VERSION=11.0.0 \\"
echo "  chronos-speechbot-amd \\"
echo "  python3 -c 'import torch; print(f\"CUDA available: {torch.cuda.is_available()}\")'"

echo ""

# Summary
echo "📋 Configuration Summary"
echo "======================="
echo "✅ Docker: Configured for ROCm"
echo "✅ GPU devices: /dev/kfd, /dev/dri available"
echo "⚠️ User groups: Still need to be configured (video, render)"
echo ""
echo "Next steps:"
echo "1. Add user to video/render groups:"
echo "   sudo usermod -a -G video,render $USER"
echo "2. Log out and back in (or restart)"
echo "3. Test GPU access with the commands above"
echo "4. Launch Speechbot with AMD acceleration"

echo ""
echo "🎯 Ready for AMD RX 7900 XT GPU acceleration!"
